.PHONY: install install-dev test test-cov lint format type-check run web build clean help pre-commit all-checks

# 默认目标
.DEFAULT_GOAL := help

# 安装依赖
install:
	uv sync

# 安装开发依赖
install-dev:
	uv sync --extra dev --extra test --extra docs

# 运行导出功能
run:
	deepresearch-export export $(ARGS)

# 启动Web界面
web:
	deepresearch-export web $(ARGS)

# 启动Web界面（绑定到0.0.0.0）
web-server:
	deepresearch-export web -k server_name=0.0.0.0

# 测试
test:
	pytest

test-cov:
	pytest --cov=deepresearch_exporter --cov-report=html --cov-report=term

# 代码质量
lint:
	flake8 deepresearch_exporter tests

format:
	black deepresearch_exporter tests
	isort deepresearch_exporter tests

type-check:
	mypy deepresearch_exporter

# Pre-commit
pre-commit:
	pre-commit install
	pre-commit run --all-files

# 运行所有检查
all-checks: lint type-check test
	@echo "All checks passed!"

# 构建分发包
build: clean
	python -m build

# 清理临时文件
clean:
	rm -rf output/ __pycache__/ .pytest_cache/ dist/ build/ *.egg-info/
	rm -rf .mypy_cache/ htmlcov/ .coverage
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -delete

# 帮助信息
help:
	@echo "可用命令:"
	@echo "  make install      - 安装项目"
	@echo "  make install-dev  - 安装开发依赖"
	@echo "  make test         - 运行测试"
	@echo "  make test-cov     - 运行测试并生成覆盖率报告"
	@echo "  make lint         - 代码检查"
	@echo "  make format       - 代码格式化"
	@echo "  make type-check   - 类型检查"
	@echo "  make pre-commit   - 安装并运行pre-commit钩子"
	@echo "  make all-checks   - 运行所有质量检查"
	@echo "  make run ARGS='input.json -o output/'  - 运行导出功能"
	@echo "  make web ARGS='-v'  - 启动Web界面"
	@echo "  make web-server   - 启动Web界面并绑定到0.0.0.0"
	@echo "  make build        - 构建分发包"
	@echo "  make clean        - 清理临时文件"