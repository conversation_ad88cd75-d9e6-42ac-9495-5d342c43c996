[project]
name = "deepresearch-exporter"
version = "0.1.0"
description = "导出和格式化ChatGPT Deep Research模式的对话内容"
readme = "README.md"
requires-python = ">=3.12"
keywords = ["chatgpt", "deep-research", "markdown", "bibtex", "academic", "export", "citation"]
dependencies = [
    "pydantic>=2.0.0",
    "click>=8.0.0",
    "pybtex>=0.24.0",
    "rich>=14.0.0",
    "gradio>=5.32.0",
    "aiohttp>=3.8.0",
    "llm-translator",
    "bibtexparser>=1.4.3",
    "deepl>=1.22.0",
    "googletrans>=4.0.2",
    "python-dotenv>=1.0.0",
    "pytest>=8.4.1",
]
authors = [
    {name = "Zenor0", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Researchers",
    "Intended Audience :: Education",
    "Topic :: Scientific/Engineering",
    "Topic :: Text Processing :: Markup",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-asyncio>=0.21.0",
    "coverage>=7.0.0",
]
dev = [
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "types-requests>=2.28.0",
]
docs = [
    "mkdocs>=1.4.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.20.0",
]
all = [
    "deepresearch-exporter[test,dev,docs]"
]

[project.urls]
Homepage = "https://github.com/zenor0/deepresearch-exporter"
Repository = "https://github.com/zenor0/deepresearch-exporter"
Documentation = "https://github.com/zenor0/deepresearch-exporter#readme"
"Bug Tracker" = "https://github.com/zenor0/deepresearch-exporter/issues"

[project.scripts]
deepresearch-export = "deepresearch_exporter.cli:main"
deepresearch-web = "deepresearch_exporter.cli:web"

[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["deepresearch_exporter"]

# ===== 工具配置 =====

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["deepresearch_exporter"]
known_third_party = ["pytest", "click", "gradio", "pydantic", "rich"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "bibtexparser.*",
    "pybtex.*",
    "deepl.*",
    "googletrans.*",
    "gradio.*",
]
ignore_missing_imports = true

[tool.coverage.run]
source = ["deepresearch_exporter"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/venv/*",
    "*/env/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
    "--color=yes",
    "--durations=10",
    "--cov=deepresearch_exporter",
    "--cov-report=term-missing",
    "--cov-report=html",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "ui: marks tests as UI tests",
    "api: marks tests as API tests",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.uv.sources]
llm-translator = { path = "manifest/llm_translator-0.1.0-py3-none-any.whl" }

