[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (handled by isort)
    F401,
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    build,
    dist,
    *.egg-info,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov,
    docs/_build,
    migrations,
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401,F403
    # Test files can have unused imports and long lines
    tests/*:F401,F403,E501
    # Configuration files
    conftest.py:F401,F403
max-complexity = 10
docstring-convention = google
import-order-style = google
