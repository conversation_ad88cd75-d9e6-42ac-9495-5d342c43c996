"""
翻译模块，提供文本翻译功能
"""

import json
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import os
import tempfile

from .models import TranslationConfig, TranslationResult
from .logger import setup_logger

logger = setup_logger(name=__name__)


class Translator:
    """翻译器类，负责与LLM API交互进行翻译"""

    def __init__(self, config: TranslationConfig):
        """
        初始化翻译器

        Args:
            config: 翻译配置
        """
        self.config = config

    async def _translate_text_async(self, text: str) -> TranslationResult:
        """
        异步翻译单个文本

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        if not text.strip():
            return TranslationResult(original_text=text, translation="")

        # 构建提示
        system_prompt = "你是一个专业的翻译助手。"
        if self.config.domain:
            system_prompt += f"你精通{self.config.domain}领域的专业术语和表达方式。"

        system_prompt += f"请将{self.config.source_language}文本翻译成{self.config.target_language}。"
        system_prompt += (
            "保持原文的格式和结构，包括段落、列表、标题等。保留原文中的Markdown格式。"
        )

        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Content-Type": "application/json"}

                if self.config.api_key and self.config.api_key != "ollama":
                    headers["Authorization"] = f"Bearer {self.config.api_key}"

                payload = {
                    "model": self.config.model,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": text},
                    ],
                    "temperature": self.config.temperature,
                    "top_p": self.config.top_p,
                }

                async with session.post(
                    f"{self.config.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"翻译API错误: {response.status}, {error_text}")
                        return TranslationResult(
                            original_text=text,
                            error=f"API错误 {response.status}: {error_text}",
                        )

                    data = await response.json()
                    translation = data["choices"][0]["message"]["content"]
                    return TranslationResult(
                        original_text=text, translation=translation
                    )

        except Exception as e:
            logger.error(f"翻译过程中发生错误: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    def translate_text(self, text: str) -> TranslationResult:
        """
        翻译单个文本

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        return asyncio.run(self._translate_text_async(text))

    async def _translate_batch_async(self, texts: List[str]) -> List[TranslationResult]:
        """
        异步批量翻译文本

        Args:
            texts: 要翻译的文本列表

        Returns:
            翻译结果列表
        """
        tasks = []
        for text in texts:
            tasks.append(self._translate_text_async(text))

        # 限制并发请求数
        semaphore = asyncio.Semaphore(self.config.max_requests)

        async def bounded_translate(text):
            async with semaphore:
                return await self._translate_text_async(text)

        bounded_tasks = [bounded_translate(text) for text in texts]
        return await asyncio.gather(*bounded_tasks)

    def translate_batch(self, texts: List[str]) -> List[TranslationResult]:
        """
        批量翻译文本

        Args:
            texts: 要翻译的文本列表

        Returns:
            翻译结果列表
        """
        return asyncio.run(self._translate_batch_async(texts))

    def _split_text(self, text: str, max_tokens: Optional[int] = None) -> List[str]:
        """
        将长文本分割为多个段落

        Args:
            text: 要分割的文本
            max_tokens: 每个段落的最大token数，默认使用配置中的值

        Returns:
            段落列表
        """
        if max_tokens is None:
            max_tokens = self.config.max_tokens

        # 简单的按段落分割
        paragraphs = text.split("\n\n")
        chunks = []
        current_chunk = ""

        for para in paragraphs:
            # 这里使用简单的字符数作为token数的估计
            # 实际应用中可能需要更精确的token计算
            if len(current_chunk) + len(para) > max_tokens:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = para
            else:
                if current_chunk:
                    current_chunk += "\n\n" + para
                else:
                    current_chunk = para

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    def translate_long_text(self, text: str) -> TranslationResult:
        """
        翻译长文本，自动分段处理

        Args:
            text: 要翻译的长文本

        Returns:
            翻译结果
        """
        if not text.strip():
            return TranslationResult(original_text=text, translation="")

        try:
            # 分割文本
            chunks = self._split_text(text)

            # 如果只有一个段落，直接翻译
            if len(chunks) == 1:
                return self.translate_text(text)

            # 批量翻译
            batch_size = self.config.batch_size
            all_results = []

            for i in range(0, len(chunks), batch_size):
                batch = chunks[i : i + batch_size]
                results = self.translate_batch(batch)
                all_results.extend(results)

            # 检查是否有错误
            errors = [r.error for r in all_results if r.error]
            if errors:
                return TranslationResult(
                    original_text=text,
                    error=f"部分翻译失败: {'; '.join(errors[:3])}{'...' if len(errors) > 3 else ''}",
                )

            # 合并结果
            translated_text = "\n\n".join([r.translation for r in all_results])
            return TranslationResult(original_text=text, translation=translated_text)

        except Exception as e:
            logger.error(f"翻译长文本时发生错误: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    def translate_file(
        self, input_path: str, output_path: Optional[str] = None
    ) -> Tuple[str, Optional[str]]:
        """
        翻译文本文件

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            (输出文件路径, 错误信息)
        """
        try:
            # 读取文件
            with open(input_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 翻译内容
            result = self.translate_long_text(content)

            if result.error:
                return "", result.error

            # 确定输出路径
            if not output_path:
                base, ext = os.path.splitext(input_path)
                output_path = f"{base}_translated{ext}"

            # 写入翻译结果
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result.translation)

            return output_path, None

        except Exception as e:
            logger.error(f"翻译文件时发生错误: {str(e)}")
            return "", str(e)
