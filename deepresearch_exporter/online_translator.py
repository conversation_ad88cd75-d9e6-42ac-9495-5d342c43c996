"""
在线翻译模块，提供基于Google Translate和DeepL的翻译功能
"""

import os
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from deepresearch_exporter.models import TranslationResult
from deepresearch_exporter.logger import setup_logger

# 导入第三方翻译库
try:
    from googletrans import Translator as GoogleTranslator

    GOOGLE_TRANSLATE_AVAILABLE = True
except ImportError:
    GOOGLE_TRANSLATE_AVAILABLE = False

try:
    import deepl

    DEEPL_AVAILABLE = True
except ImportError:
    DEEPL_AVAILABLE = False

logger = setup_logger(name=__name__)


class TranslatorType(str, Enum):
    """翻译器类型枚举"""

    GOOGLE = "google"
    DEEPL = "deepl"


class OnlineTranslationConfig:
    """在线翻译配置类 - 保持向后兼容"""

    def __init__(
        self,
        translator_type: TranslatorType = TranslatorType.GOOGLE,
        api_key: str = "",
        source_language: Optional[str] = None,
        target_language: str = "zh-CN",
        formality: Optional[
            str
        ] = None,  # DeepL特有的参数，可选："default", "more", "less"
        preserve_formatting: bool = False,  # 是否保留格式
        split_sentences: str = "nonewlines",  # 分句策略："nonewlines", "default", "no"
        tag_handling: Optional[str] = None,  # 标签处理方式："xml", "html"
        model_type: Optional[
            str
        ] = None,  # DeepL模型类型："quality_optimized", "prefer_quality_optimized"
    ):
        self.translator_type = translator_type
        self.api_key = api_key
        self.source_language = source_language
        self.target_language = target_language
        self.formality = formality
        self.preserve_formatting = preserve_formatting
        self.split_sentences = split_sentences
        self.tag_handling = tag_handling
        self.model_type = model_type

    @classmethod
    def from_config(cls, config=None):
        """从配置模块创建实例"""
        if config is None:
            from .config import get_online_translation_config
            config = get_online_translation_config()

        # 转换字符串类型为枚举
        translator_type = (
            TranslatorType.GOOGLE
            if config.translator_type.lower() == "google"
            else TranslatorType.DEEPL
        )

        return cls(
            translator_type=translator_type,
            api_key=config.api_key,
            source_language=config.source_language,
            target_language=config.target_language,
            formality=config.formality,
            preserve_formatting=config.preserve_formatting,
            split_sentences=config.split_sentences,
            tag_handling=config.tag_handling,
            model_type=config.model_type,
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "translator_type": self.translator_type,
            "api_key": self.api_key,
            "source_language": self.source_language,
            "target_language": self.target_language,
            "formality": self.formality,
            "preserve_formatting": self.preserve_formatting,
            "split_sentences": self.split_sentences,
            "tag_handling": self.tag_handling,
            "model_type": self.model_type,
        }


class OnlineTranslator:
    """在线翻译器类，支持Google Translate和DeepL API"""

    def __init__(self, config: OnlineTranslationConfig):
        """
        初始化翻译器

        Args:
            config: 翻译配置
        """
        self.config = config
        self._init_translator()

    def _init_translator(self):
        """初始化具体的翻译器实例"""
        if self.config.translator_type == TranslatorType.GOOGLE:
            if not GOOGLE_TRANSLATE_AVAILABLE:
                raise ImportError(
                    "Google Translate库未安装，请使用pip install googletrans==4.0.0-rc1安装"
                )
            self.translator = GoogleTranslator()

        elif self.config.translator_type == TranslatorType.DEEPL:
            if not DEEPL_AVAILABLE:
                raise ImportError("DeepL API库未安装，请使用pip install deepl安装")
            if not self.config.api_key:
                raise ValueError("使用DeepL翻译需要提供API密钥")
            self.translator = deepl.Translator(self.config.api_key)

        else:
            raise ValueError(f"不支持的翻译器类型: {self.config.translator_type}")

    async def _translate_with_google(self, text: str) -> TranslationResult:
        """
        使用Google Translate翻译

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        try:
            # 确保source_language不为None，如果是None则省略该参数让googletrans自动检测语言
            kwargs = {"text": text, "dest": self.config.target_language or "zh-CN"}

            # 只有当source_language不为None时才添加src参数
            if (
                self.config.source_language
                and self.config.source_language.lower() != "auto"
            ):
                kwargs["src"] = self.config.source_language

            # 使用**kwargs传递参数，避免None值
            result = await self.translator.translate(**kwargs)
            return TranslationResult(original_text=text, translation=result.text)
        except Exception as e:
            from traceback import format_exc

            print(format_exc())
            logger.error(f"Google翻译失败: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    async def _translate_with_deepl(self, text: str) -> TranslationResult:
        """
        使用DeepL API翻译

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        try:
            # DeepL 支持的语言代码映射，参考：https://developers.deepl.com/docs/getting-started/supported-languages
            deepl_source_lang_map = {
                "zh-CN": "ZH",  # 中文
                "zh-TW": "ZH",  # 中文
                "zh": "ZH",  # 中文
                "en-US": "EN",  # 英语
                "en-GB": "EN",  # 英语
                "en": "EN",  # 英语
                "bg": "BG",  # 保加利亚语
                "cs": "CS",  # 捷克语
                "da": "DA",  # 丹麦语
                "de": "DE",  # 德语
                "el": "EL",  # 希腊语
                "es": "ES",  # 西班牙语
                "et": "ET",  # 爱沙尼亚语
                "fi": "FI",  # 芬兰语
                "fr": "FR",  # 法语
                "hu": "HU",  # 匈牙利语
                "id": "ID",  # 印度尼西亚语
                "it": "IT",  # 意大利语
                "ja": "JA",  # 日语
                "ko": "KO",  # 韩语
                "lt": "LT",  # 立陶宛语
                "lv": "LV",  # 拉脱维亚语
                "nb": "NB",  # 挪威语
                "nl": "NL",  # 荷兰语
                "pl": "PL",  # 波兰语
                "pt": "PT",  # 葡萄牙语
                "ro": "RO",  # 罗马尼亚语
                "ru": "RU",  # 俄语
                "sk": "SK",  # 斯洛伐克语
                "sl": "SL",  # 斯洛文尼亚语
                "sv": "SV",  # 瑞典语
                "tr": "TR",  # 土耳其语
                "uk": "UK",  # 乌克兰语
            }

            # DeepL 支持的目标语言代码映射
            deepl_target_lang_map = {
                **deepl_source_lang_map,  # 包含所有源语言
                "zh-CN": "ZH",  # 中文(简体)
                "zh-TW": "ZH",  # 中文(繁体)，使用ZH
                "en-US": "EN-US",  # 美式英语
                "en-GB": "EN-GB",  # 英式英语
                "en": "EN",  # 英语
                "pt-BR": "PT-BR",  # 巴西葡萄牙语
                "pt-PT": "PT-PT",  # 欧洲葡萄牙语
                "pt": "PT",  # 葡萄牙语
            }

            # 获取目标语言的DeepL代码
            target_lang = deepl_target_lang_map.get(
                self.config.target_language, self.config.target_language
            )

            kwargs = {
                "text": text,
                "target_lang": target_lang,  # 使用映射后的语言代码
            }

            # 添加可选参数 - source_lang为空时DeepL会自动检测语言
            if self.config.source_language:
                source_lang = deepl_source_lang_map.get(
                    self.config.source_language, self.config.source_language
                )
                kwargs["source_lang"] = source_lang

            # 添加形式参数（正式/非正式）
            if self.config.formality:
                kwargs["formality"] = self.config.formality

            # 添加其他可选参数
            if self.config.preserve_formatting:
                kwargs["preserve_formatting"] = self.config.preserve_formatting

            # 添加拆分句子参数
            kwargs["split_sentences"] = self.config.split_sentences

            # 添加标签处理方式
            if self.config.tag_handling:
                kwargs["tag_handling"] = self.config.tag_handling

            # 添加模型类型参数
            if self.config.model_type:
                kwargs["model_type"] = self.config.model_type

            # 使用线程池执行非异步的DeepL API调用
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, lambda: self.translator.translate_text(**kwargs)
            )

            # DeepL API返回的可能是单个结果或列表
            if isinstance(result, list):
                translation = result[0].text
            else:
                translation = result.text

            return TranslationResult(original_text=text, translation=translation)
        except Exception as e:
            logger.error(f"DeepL翻译失败: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    async def translate_text_async(self, text: str) -> TranslationResult:
        """
        异步翻译单个文本

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        if not text.strip():
            return TranslationResult(original_text=text, translation="")

        try:
            if self.config.translator_type == TranslatorType.GOOGLE:
                return await self._translate_with_google(text)
            elif self.config.translator_type == TranslatorType.DEEPL:
                return await self._translate_with_deepl(text)
            else:
                return TranslationResult(
                    original_text=text,
                    error=f"不支持的翻译器类型: {self.config.translator_type}",
                )
        except Exception as e:
            logger.error(f"翻译过程中发生错误: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    def translate_text(self, text: str) -> TranslationResult:
        """
        翻译单个文本（同步版本）

        Args:
            text: 要翻译的文本

        Returns:
            翻译结果
        """
        return asyncio.run(self.translate_text_async(text))

    async def translate_batch_async(self, texts: List[str]) -> List[TranslationResult]:
        """
        异步批量翻译文本，一次处理所有文本

        Args:
            texts: 要翻译的文本列表

        Returns:
            翻译结果列表
        """
        results = []
        for text in texts:
            result = await self.translate_text_async(text)
            results.append(result)
        return results

    def translate_batch(self, texts: List[str]) -> List[TranslationResult]:
        """
        批量翻译文本

        Args:
            texts: 要翻译的文本列表

        Returns:
            翻译结果列表
        """
        if not texts:
            return []

        try:
            return asyncio.run(self.translate_batch_async(texts))
        except Exception as e:
            logger.error(f"批量翻译失败: {str(e)}")
            results = []
            for text in texts:
                results.append(TranslationResult(original_text=text, error=str(e)))
            return results

    def _split_text(self, text: str, max_length: int = 5000) -> List[str]:
        """
        将长文本分割为多个段落

        Args:
            text: 要分割的文本
            max_length: 每个段落的最大长度，默认5000字符

        Returns:
            段落列表
        """
        # 简单的按段落分割
        paragraphs = text.split("\n\n")
        chunks = []
        current_chunk = ""

        for para in paragraphs:
            if len(current_chunk) + len(para) > max_length:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = para
            else:
                if current_chunk:
                    current_chunk += "\n\n" + para
                else:
                    current_chunk = para

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

    async def translate_long_text_async(self, text: str) -> TranslationResult:
        """
        异步翻译长文本，自动分段处理

        Args:
            text: 要翻译的长文本

        Returns:
            翻译结果
        """
        if not text.strip():
            return TranslationResult(original_text=text, translation="")

        try:
            # 使用DeepL时直接翻译整个文本，不进行分段处理
            if self.config.translator_type == TranslatorType.DEEPL:
                return await self.translate_text_async(text)

            # 对于Google Translate，继续使用分段处理
            # 分割文本
            chunks = self._split_text(text)

            # 如果只有一个段落，直接翻译
            if len(chunks) == 1:
                return await self.translate_text_async(text)

            # 翻译所有段落
            results = []
            for chunk in chunks:
                result = await self.translate_text_async(chunk)
                results.append(result)

            # 检查是否有错误
            errors = [r.error for r in results if r.error]
            if errors:
                return TranslationResult(
                    original_text=text,
                    error=f"部分翻译失败: {'; '.join(errors[:3])}{'...' if len(errors) > 3 else ''}",
                )

            # 合并结果
            translated_text = "\n\n".join([r.translation for r in results])
            return TranslationResult(original_text=text, translation=translated_text)

        except Exception as e:
            logger.error(f"翻译长文本时发生错误: {str(e)}")
            return TranslationResult(original_text=text, error=str(e))

    def translate_long_text(self, text: str) -> TranslationResult:
        """
        翻译长文本，自动分段处理（同步版本）

        Args:
            text: 要翻译的长文本

        Returns:
            翻译结果
        """
        return asyncio.run(self.translate_long_text_async(text))

    def translate_file(
        self, input_path: str, output_path: Optional[str] = None
    ) -> Tuple[str, Optional[str]]:
        """
        翻译文本文件

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            (输出文件路径, 错误信息)
        """
        try:
            # 读取文件
            with open(input_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 使用同步版本的长文本翻译方法
            result = self.translate_long_text(content)

            if result.error:
                return "", result.error

            # 确定输出路径
            if not output_path:
                base, ext = os.path.splitext(input_path)
                output_path = f"{base}_translated{ext}"

            # 写入翻译结果
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(result.translation)

            return output_path, None

        except Exception as e:
            logger.error(f"翻译文件时发生错误: {str(e)}")
            return "", str(e)
