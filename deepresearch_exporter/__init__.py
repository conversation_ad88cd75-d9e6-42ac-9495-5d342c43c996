"""
DeepResearch Exporter - 导出和格式化ChatGPT Deep Research模式的对话内容

此包提供了解析ChatGPT导出的Deep Research对话，并将其导出为Markdown和BibTeX格式的功能。
"""

# 核心模型
from .models import DeepResearchResponse, Citation

# 核心功能（新结构）
from .core import ConversationProcessor, DocumentExporter, CitationValidator

# 服务
from .services import TranslationService

# 工具
from .tools import MarkdownProcessor, BibtexProcessor

# 配置
from .config import (
    get_config, reload_config,
    get_llm_translation_config, get_online_translation_config,
    get_export_config, get_ui_config, get_logging_config
)

# 日志
from .logger import setup_logger, logger

# UI
from .ui import create_ui, launch_ui

# 向后兼容的导入
from .parser import process_conversation
from .exporter import export_response, export_response_with_docx, export_docx

__version__ = "0.1.0"

__all__ = [
    # 核心模型
    "DeepResearchResponse",
    "Citation",

    # 核心功能（新结构）
    "ConversationProcessor",
    "DocumentExporter",
    "CitationValidator",

    # 服务
    "TranslationService",

    # 工具
    "MarkdownProcessor",
    "BibtexProcessor",

    # 配置
    "get_config",
    "reload_config",
    "get_llm_translation_config",
    "get_online_translation_config",
    "get_export_config",
    "get_ui_config",
    "get_logging_config",

    # 日志
    "setup_logger",
    "logger",

    # UI
    "create_ui",
    "launch_ui",

    # 向后兼容
    "process_conversation",
    "export_response",
    "export_response_with_docx",
    "export_docx",
]
