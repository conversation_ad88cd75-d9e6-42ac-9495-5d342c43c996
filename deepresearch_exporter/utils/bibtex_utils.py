"""
BibTeX解析工具，不依赖pybtex库，适用于Python 3.12
"""

import re
from typing import Dict, List
from pathlib import Path

from ..logger import setup_logger
from ..models import BibTexEntry

# 配置日志
logger = setup_logger(name=__name__)


def parse_bibtex_file(bib_path: str) -> Dict[str, BibTexEntry]:
    """
    解析BibTeX文件并返回条目字典

    Args:
        bib_path: BibTeX文件路径

    Returns:
        包含BibTeX条目的字典，键为citekey
    """
    entries = {}

    try:
        with open(bib_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 匹配BibTeX条目的正则表达式
        # 匹配@类型{键,字段}的格式
        entry_pattern = r"@(\w+)\s*\{\s*([^,]+),\s*((?:.|\n)+?)\s*\}"
        entry_matches = re.finditer(entry_pattern, content, re.MULTILINE)

        for match in entry_matches:
            entry_type = match.group(1).lower()  # 类型，如article, book
            citation_key = match.group(2).strip()  # 引用键
            fields_text = match.group(3)  # 所有字段文本

            # 解析字段
            fields = parse_bibtex_fields(fields_text)

            # 处理作者
            authors = []
            if "author" in fields:
                author_str = fields["author"]
                authors = parse_bibtex_authors(author_str)

            # 创建BibTeX条目
            entry = BibTexEntry(
                entry_type=entry_type,
                key=citation_key,
                title=fields.get("title"),
                author=fields.get("author"),
                authors=authors,
                year=fields.get("year"),
                journal=fields.get("journal"),
                volume=fields.get("volume"),
                number=fields.get("number"),
                pages=fields.get("pages"),
                publisher=fields.get("publisher"),
                booktitle=fields.get("booktitle"),
                url=fields.get("url"),
                doi=fields.get("doi"),
            )

            # 添加其他字段
            for key, value in fields.items():
                if not hasattr(entry, key) or getattr(entry, key) is None:
                    entry.extra_fields[key] = value

            entries[citation_key] = entry

        return entries
    except Exception as e:
        logger.error(f"解析BibTeX文件失败: {str(e)}")
        return {}


def parse_bibtex_fields(fields_text: str) -> Dict[str, str]:
    """
    解析BibTeX字段文本

    Args:
        fields_text: 包含字段的文本

    Returns:
        字段字典
    """
    fields = {}

    # 找出所有可能的字段
    offset = 0
    remaining_text = fields_text

    while offset < len(fields_text):
        # 查找下一个字段名
        key_match = re.search(r"(\w+)\s*=\s*", remaining_text)
        if not key_match:
            break

        key = key_match.group(1).lower()
        value_start = key_match.end()

        # 跳过key=部分
        remaining_text = remaining_text[value_start:]
        offset += value_start

        # 根据下一个字符判断值的类型
        first_char = remaining_text[0] if remaining_text else ""

        if first_char == "{":
            # 大括号值，需要处理嵌套
            brace_count = 1
            value_end = 0

            for i, char in enumerate(remaining_text[1:], 1):
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        value_end = i
                        break

            if value_end > 0:
                value = remaining_text[1:value_end].strip()
                fields[key] = value
                remaining_text = remaining_text[value_end + 1 :].lstrip()
                offset += value_end + 1

        elif first_char == '"':
            # 引号值
            quote_end = remaining_text[1:].find('"')
            if quote_end >= 0:
                value = remaining_text[1 : quote_end + 1].strip()
                fields[key] = value
                remaining_text = remaining_text[quote_end + 2 :].lstrip()
                offset += quote_end + 2

        elif first_char.isdigit():
            # 数字值
            num_match = re.match(r"\d+", remaining_text)
            if num_match:
                value = num_match.group(0)
                fields[key] = value
                remaining_text = remaining_text[len(value) :].lstrip()
                offset += len(value)

        else:
            # 跳过无法解析的部分
            remaining_text = remaining_text[1:].lstrip()
            offset += 1

    return fields


def clean_nested_braces(text: str) -> str:
    """
    清理文本中嵌套的大括号

    Args:
        text: 输入文本

    Returns:
        清理后的文本
    """
    # 移除最外层的大括号
    if text.startswith("{") and text.endswith("}"):
        text = text[1:-1]

    # 替换 {\i} 这样的LaTeX命令为简单字符
    text = re.sub(r"\{\\[a-z]+\}", "", text)

    return text


def parse_bibtex_authors(author_str: str) -> List[str]:
    """
    解析BibTeX作者字符串

    Args:
        author_str: 作者字符串

    Returns:
        作者列表
    """
    # 去除大括号和多余空格
    author_str = author_str.strip()
    if author_str.startswith("{") and author_str.endswith("}"):
        author_str = author_str[1:-1]

    # 按 "and" 分割作者
    authors = [a.strip() for a in re.split(r"\s+and\s+", author_str)]

    # 处理每个作者名字
    result = []
    for author in authors:
        # 清理名字中的LaTeX命令
        author = re.sub(r"\\[a-z]+\{([^{}]*)\}", r"\1", author)
        if author:
            result.append(author)

    return result


def format_bibtex_entry(entry: Dict[str, str]) -> str:
    """
    格式化BibTeX条目为字符串

    Args:
        entry: BibTeX条目字典

    Returns:
        格式化后的BibTeX字符串
    """
    if not entry:
        return ""

    entry_type = entry.get("type", "misc")
    key = entry.get("key", "unknown")

    # 构建字段列表
    fields = []

    # 常见字段的顺序
    field_order = ["author", "title", "journal", "year", "volume", "number", "pages", "publisher", "url", "doi"]

    for field in field_order:
        if field in entry and entry[field]:
            value = entry[field]
            fields.append(f"  {field} = {{{value}}}")

    # 添加其他字段
    for field, value in entry.items():
        if field not in field_order and field not in ["type", "key"] and value:
            fields.append(f"  {field} = {{{value}}}")

    fields_str = ",\n".join(fields)
    return f"@{entry_type}{{{key},\n{fields_str}\n}}"


def validate_bibtex_entry(entry: Dict[str, str]) -> bool:
    """
    验证BibTeX条目是否有效

    Args:
        entry: BibTeX条目字典

    Returns:
        是否有效
    """
    if not entry:
        return False

    # 检查必需字段
    if not entry.get("key"):
        return False

    if not entry.get("title"):
        return False

    # 检查条目类型
    entry_type = entry.get("type", "").lower()
    if entry_type not in ["article", "book", "inproceedings", "misc", "techreport", "phdthesis", "mastersthesis"]:
        return False

    return True


def normalize_bibtex_key(key: str) -> str:
    """
    规范化BibTeX键

    Args:
        key: 原始键

    Returns:
        规范化后的键
    """
    if not key:
        return "unknown"

    # 移除特殊字符，只保留字母、数字和下划线
    normalized = re.sub(r"[^a-zA-Z0-9_]", "", key)

    # 确保以字母开头
    if normalized and not normalized[0].isalpha():
        normalized = "key_" + normalized

    # 如果为空，返回默认值
    if not normalized:
        normalized = "unknown"

    return normalized
