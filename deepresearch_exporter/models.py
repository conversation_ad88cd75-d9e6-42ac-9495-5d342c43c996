"""
数据模型定义，用于解析ChatGPT导出的JSON数据
"""

from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field


class Author(BaseModel):
    """作者信息"""

    role: str
    name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class Content(BaseModel):
    """消息内容部分"""

    content_type: str = Field(alias="content_type")
    parts: Optional[List[str]] = None
    language: Optional[str] = None
    response_format_name: Optional[str] = None
    text: Optional[str] = None

    def get_text(self) -> str:
        """获取内容文本"""
        if self.parts:
            return "\n".join(self.parts)
        elif self.text:
            return self.text
        return ""


class Message(BaseModel):
    """单条消息"""

    id: str
    author: Author
    create_time: Optional[float] = Field(alias="create_time", default=None)
    update_time: Optional[float] = Field(alias="update_time", default=None)
    content: Content
    status: str
    end_turn: Optional[bool] = Field(alias="end_turn", default=None)
    weight: float
    metadata: Optional[Dict[str, Any]] = None
    recipient: Optional[str] = None
    channel: Optional[str] = None


class MessageNode(BaseModel):
    """消息节点"""

    id: str
    message: Optional[Message] = None
    parent: Optional[str] = None
    children: List[str] = []


class Conversation(BaseModel):
    """对话数据"""

    title: str
    create_time: float = Field(alias="create_time")
    update_time: float = Field(alias="update_time")
    mapping: Dict[str, MessageNode]
    moderation_results: List[Any] = []
    current_node: str = Field(alias="current_node")
    conversation_id: Optional[str] = Field(alias="conversation_id", default=None)
    conversation_template_id: Optional[str] = Field(
        alias="conversation_template_id", default=None
    )

    model_config = {
        "extra": "ignore"  # 忽略额外的字段
    }


class CitationMetadata(BaseModel):
    """引用元数据"""

    type: Optional[str] = None
    title: str
    url: Optional[str] = None
    text: Optional[str] = None
    pub_date: Optional[str] = None
    extra: Optional[Dict[str, Any]] = None
    og_tags: Optional[Dict[str, str]] = None


class CitationInfo(BaseModel):
    """引用信息"""

    start_ix: int
    end_ix: int
    citation_format_type: str
    metadata: CitationMetadata


class BibTexEntry(BaseModel):
    """BibTeX条目模型"""

    entry_type: str  # article, book, inproceedings等
    key: str  # 引用键
    # 常见字段
    title: Optional[str] = None
    author: Optional[str] = None  # 原始作者字符串
    authors: List[str] = []  # 解析后的作者列表
    year: Optional[str] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    number: Optional[str] = None
    pages: Optional[str] = None
    publisher: Optional[str] = None
    booktitle: Optional[str] = None
    url: Optional[str] = None
    doi: Optional[str] = None
    # 其他字段可以存储在这里
    extra_fields: Dict[str, str] = {}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "key": self.key,
            "type": self.entry_type,
            "authors": self.authors,
            "title": self.title or "",
            "year": self.year or "",
        }

        # 确保URL字段存在
        result["url"] = self.url or ""
        if not result["url"] and self.doi:
            result["url"] = f"https://doi.org/{self.doi}"

        # 添加其他重要字段
        result["doi"] = self.doi or ""
        result["journal"] = self.journal or ""
        result["publisher"] = self.publisher or ""

        # 检查extra_fields中的URL
        if not result["url"]:
            for field_key, field_value in self.extra_fields.items():
                if field_key in ["url", "link", "web_url"] and field_value:
                    result["url"] = field_value
                    break

        # 添加其他字段
        result.update(self.extra_fields)

        return result


class Citation(BaseModel):
    """引用信息"""

    key: str
    title: str
    authors: List[str] = []
    year: Optional[Union[str, int]] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    pages: Optional[str] = None
    url: Optional[str] = None
    doi: Optional[str] = None
    publisher: Optional[str] = None
    error: Optional[bool] = False
    text: Optional[str] = None  # 引用的原始文本
    entry_type: Optional[str] = None  # BibTeX entry type

    def to_bibtex(self) -> str:
        """转换为BibTeX格式"""
        author_str = " and ".join(self.authors) if self.authors else "Unknown"

        fields = [f"author={{{author_str}}}", f"title={{{self.title}}}"]

        if self.year:
            fields.append(f"year={{{self.year}}}")
        if self.journal:
            fields.append(f"journal={{{self.journal}}}")
        if self.volume:
            fields.append(f"volume={{{self.volume}}}")
        if self.pages:
            fields.append(f"pages={{{self.pages}}}")
        if self.url:
            fields.append(f"url={{{self.url}}}")
        if self.doi:
            fields.append(f"doi={{{self.doi}}}")
        if self.publisher:
            fields.append(f"publisher={{{self.publisher}}}")

        fields_str = ",\n  ".join(fields)

        # Determine entry type
        entry_type = self.entry_type or ("article" if self.journal else "misc")

        return f"@{entry_type}{{{self.key},\n  {fields_str}\n}}"

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Citation":
        """从字典创建Citation实例"""
        return cls(**data)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "key": self.key,
            "title": self.title,
            "authors": self.authors,
            "year": self.year,
            "journal": self.journal,
            "volume": self.volume,
            "pages": self.pages,
            "url": self.url,
            "doi": self.doi,
            "publisher": self.publisher,
            "error": self.error,
            "text": self.text,
            "entry_type": self.entry_type,
        }


class DeepResearchResponse(BaseModel):
    """Deep Research模式的回复内容"""

    content: str
    processed_content: Optional[str] = None
    original_content: Optional[str] = None
    citations: List[Citation] = []
    metadata: Optional[Dict[str, Any]] = None

    def __init__(self, **data):
        # 向后兼容处理
        if (
            "content" in data
            and "processed_content" not in data
            and "original_content" not in data
        ):
            data["original_content"] = data["content"]
            data["processed_content"] = data["content"]
        super().__init__(**data)

    def get_citation_keys(self) -> List[str]:
        """获取所有引用的键"""
        return [citation.key for citation in self.citations]

    def get_citations_bibtex(self) -> str:
        """获取所有引用的BibTeX格式"""
        return "\n\n".join(citation.to_bibtex() for citation in self.citations)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "content": self.content,
            "processed_content": self.processed_content,
            "original_content": self.original_content,
            "citations": [citation.to_dict() for citation in self.citations],
            "metadata": self.metadata,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DeepResearchResponse":
        """从字典创建DeepResearchResponse实例"""
        # 处理citations字段
        citations_data = data.get("citations", [])
        citations = [Citation.from_dict(c) if isinstance(c, dict) else c for c in citations_data]

        return cls(
            content=data["content"],
            processed_content=data.get("processed_content"),
            original_content=data.get("original_content"),
            citations=citations,
            metadata=data.get("metadata"),
        )


class TranslationConfig:
    """翻译配置类 - 保持向后兼容"""

    def __init__(
        self,
        base_url: str = "http://localhost:11434/v1",
        api_key: str = "ollama",
        model: str = "qwen2.5:32b",
        source_language: str = "English",
        target_language: str = "Chinese",
        domain: Optional[str] = None,
        temperature: float = 0.3,
        top_p: float = 0.9,
        batch_size: int = 5,
        max_tokens: int = 4000,
        max_requests: int = 5,
    ):
        self.base_url = base_url
        self.api_key = api_key
        self.model = model
        self.source_language = source_language
        self.target_language = target_language
        self.domain = domain
        self.temperature = temperature
        self.top_p = top_p
        self.batch_size = batch_size
        self.max_tokens = max_tokens
        self.max_requests = max_requests

    @classmethod
    def from_config(cls, config=None):
        """从配置模块创建实例"""
        if config is None:
            from .config import get_llm_translation_config
            config = get_llm_translation_config()

        return cls(
            base_url=config.base_url,
            api_key=config.api_key,
            model=config.model,
            source_language=config.source_language,
            target_language=config.target_language,
            domain=config.domain,
            temperature=config.temperature,
            top_p=config.top_p,
            batch_size=config.batch_size,
            max_tokens=config.max_tokens,
            max_requests=config.max_requests,
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "base_url": self.base_url,
            "api_key": self.api_key,
            "model": self.model,
            "source_language": self.source_language,
            "target_language": self.target_language,
            "domain": self.domain,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "batch_size": self.batch_size,
            "max_tokens": self.max_tokens,
            "max_requests": self.max_requests,
        }


class TranslationResult:
    """翻译结果类"""

    def __init__(
        self,
        original_text: str,
        translation: Optional[str] = None,
        error: Optional[str] = None,
    ):
        self.original_text = original_text
        self.translation = translation
        self.error = error
