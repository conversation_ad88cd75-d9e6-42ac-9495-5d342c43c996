"""
翻译服务，整合LLM翻译和在线翻译功能
"""

from typing import Optional, Tuple, List
from ..models import TranslationConfig, TranslationResult
from ..translator import Translator
from ..online_translator import OnlineTranslator, OnlineTranslationConfig


class TranslationService:
    """翻译服务，提供统一的翻译接口"""
    
    def __init__(self):
        """初始化翻译服务"""
        self._llm_translator: Optional[Translator] = None
        self._online_translator: Optional[OnlineTranslator] = None
    
    def get_llm_translator(self, config: Optional[TranslationConfig] = None) -> Translator:
        """
        获取LLM翻译器实例
        
        Args:
            config: 翻译配置，如果为None则从配置模块获取
            
        Returns:
            LLM翻译器实例
        """
        if config is None:
            config = TranslationConfig.from_config()
        
        if self._llm_translator is None:
            self._llm_translator = Translator(config)
        
        return self._llm_translator
    
    def get_online_translator(self, config: Optional[OnlineTranslationConfig] = None) -> OnlineTranslator:
        """
        获取在线翻译器实例
        
        Args:
            config: 在线翻译配置，如果为None则从配置模块获取
            
        Returns:
            在线翻译器实例
        """
        if config is None:
            config = OnlineTranslationConfig.from_config()
        
        if self._online_translator is None:
            self._online_translator = OnlineTranslator(config)
        
        return self._online_translator
    
    async def translate_text_llm(
        self, 
        text: str, 
        config: Optional[TranslationConfig] = None
    ) -> TranslationResult:
        """
        使用LLM翻译文本
        
        Args:
            text: 要翻译的文本
            config: 翻译配置
            
        Returns:
            翻译结果
        """
        translator = self.get_llm_translator(config)
        return await translator.translate_text(text)
    
    async def translate_file_llm(
        self,
        file_path: str,
        output_path: str,
        config: Optional[TranslationConfig] = None
    ) -> List[TranslationResult]:
        """
        使用LLM翻译文件
        
        Args:
            file_path: 输入文件路径
            output_path: 输出文件路径
            config: 翻译配置
            
        Returns:
            翻译结果列表
        """
        translator = self.get_llm_translator(config)
        return await translator.translate_file(file_path, output_path)
    
    def translate_text_online(
        self,
        text: str,
        config: Optional[OnlineTranslationConfig] = None
    ) -> str:
        """
        使用在线服务翻译文本
        
        Args:
            text: 要翻译的文本
            config: 在线翻译配置
            
        Returns:
            翻译结果
        """
        translator = self.get_online_translator(config)
        return translator.translate_text(text)
    
    def translate_file_online(
        self,
        file_path: str,
        output_path: str,
        config: Optional[OnlineTranslationConfig] = None
    ) -> str:
        """
        使用在线服务翻译文件
        
        Args:
            file_path: 输入文件路径
            output_path: 输出文件路径
            config: 在线翻译配置
            
        Returns:
            输出文件路径
        """
        translator = self.get_online_translator(config)
        return translator.translate_file(file_path, output_path)
