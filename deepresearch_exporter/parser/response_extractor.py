"""
响应提取器模块，用于从对话中提取Deep Research模式的回复内容
"""

from typing import Optional, Set
from ..logger import setup_logger
from ..models import Conversation, DeepResearchResponse
from .detector import is_deep_research_message
from .content_processor import process_content_with_direct_references

# 配置日志
logger = setup_logger(name=__name__)


def extract_deep_research_content(
    conversation: Conversation, mode: str = "citations"
) -> Optional[DeepResearchResponse]:
    """
    从对话中提取Deep Research模式的回复内容

    Args:
        conversation: 对话数据
        mode: 引用处理模式，可选值为"citations"或"content_references"，默认为"citations"

    Returns:
        Deep Research回复内容，如果未找到则返回None
    """
    # 从当前节点开始回溯，查找Deep Research回复
    current_node_id = conversation.current_node
    visited: Set[str] = set()

    while current_node_id and current_node_id not in visited:
        visited.add(current_node_id)

        if current_node_id not in conversation.mapping:
            break

        node = conversation.mapping[current_node_id]
        if node.message and is_deep_research_message(node.message):
            # 找到Deep Research回复
            message = node.message

            # 提取内容
            content = message.content.get_text()

            # 使用直接引用处理方法
            if message.metadata:
                if mode == "citations" and message.metadata.get("citations"):
                    processed_content, citations = (
                        process_content_with_direct_references(
                            content, message, mode="citations"
                        )
                    )
                elif mode == "content_references" and message.metadata.get(
                    "content_references"
                ):
                    processed_content, citations = (
                        process_content_with_direct_references(
                            content, message, mode="content_references"
                        )
                    )
                else:
                    # 如果指定的模式下没有对应的引用信息，尝试使用另一种模式
                    if mode == "citations" and message.metadata.get(
                        "content_references"
                    ):
                        logger.warning(
                            "未找到citations引用信息，尝试使用content_references模式"
                        )
                        processed_content, citations = (
                            process_content_with_direct_references(
                                content, message, mode="content_references"
                            )
                        )
                    elif mode == "content_references" and message.metadata.get(
                        "citations"
                    ):
                        logger.warning(
                            "未找到content_references引用信息，尝试使用citations模式"
                        )
                        processed_content, citations = (
                            process_content_with_direct_references(
                                content, message, mode="citations"
                            )
                        )
                    else:
                        # 如果没有任何引用信息，直接返回原始内容
                        logger.warning("未找到任何引用信息")
                        processed_content = content
                        citations = []
            else:
                # 如果没有元数据，直接返回原始内容
                processed_content = content
                citations = []

            return DeepResearchResponse(content=processed_content, citations=citations)

        # 继续查找父节点
        current_node_id = node.parent

    # 如果没有找到Deep Research回复，尝试在所有消息中查找
    for node_id, node in conversation.mapping.items():
        if node.message and is_deep_research_message(node.message):
            # 找到Deep Research回复
            message = node.message

            # 提取内容
            content = message.content.get_text()

            # 使用直接引用处理方法
            if message.metadata:
                if mode == "citations" and message.metadata.get("citations"):
                    processed_content, citations = (
                        process_content_with_direct_references(
                            content, message, mode="citations"
                        )
                    )
                elif mode == "content_references" and message.metadata.get(
                    "content_references"
                ):
                    processed_content, citations = (
                        process_content_with_direct_references(
                            content, message, mode="content_references"
                        )
                    )
                else:
                    # 如果指定的模式下没有对应的引用信息，尝试使用另一种模式
                    if mode == "citations" and message.metadata.get(
                        "content_references"
                    ):
                        logger.warning(
                            "未找到citations引用信息，尝试使用content_references模式"
                        )
                        processed_content, citations = (
                            process_content_with_direct_references(
                                content, message, mode="content_references"
                            )
                        )
                    elif mode == "content_references" and message.metadata.get(
                        "citations"
                    ):
                        logger.warning(
                            "未找到content_references引用信息，尝试使用citations模式"
                        )
                        processed_content, citations = (
                            process_content_with_direct_references(
                                content, message, mode="citations"
                            )
                        )
                    else:
                        # 如果没有任何引用信息，直接返回原始内容
                        logger.warning("未找到任何引用信息")
                        processed_content = content
                        citations = []
            else:
                # 如果没有元数据，直接返回原始内容
                processed_content = content
                citations = []

            return DeepResearchResponse(content=processed_content, citations=citations)

    logger.warning("未找到Deep Research模式的回复")
    return None
