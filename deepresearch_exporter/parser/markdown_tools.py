"""
Markdown处理工具模块，提供调整Markdown文档标题层级的功能
"""

import re
from typing import Dict, List, Optional, Tuple, Union


def adjust_heading_levels(
    markdown_content: str,
    adjustment: int = 0,
    first_level_as_title: bool = False,
    min_level: int = 1,
    max_level: int = 6,
) -> str:
    """
    调整Markdown文档中所有标题的层级

    参数:
        markdown_content: 要处理的Markdown内容
        adjustment: 标题级别的调整量，正数增加层级，负数减少层级
        first_level_as_title: 是否将第一个一级标题保留为文章标题
        min_level: 允许的最小标题级别 (默认为1)
        max_level: 允许的最大标题级别 (默认为6)

    返回:
        处理后的Markdown内容
    """
    # 正则表达式匹配标题行
    heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

    # 如果需要将第一个一级标题保留为文章标题
    first_h1 = None
    if first_level_as_title:
        h1_match = re.search(r"^#\s+(.*?)(?:\s+#+)?$", markdown_content, re.MULTILINE)
        if h1_match:
            first_h1 = h1_match.group(0)
            first_h1_pos = h1_match.start()
            end_pos = h1_match.end()
            # 临时替换第一个一级标题，以便后续处理
            markdown_content = (
                markdown_content[:first_h1_pos]
                + "%%FIRST_TITLE_PLACEHOLDER%%"
                + markdown_content[end_pos:]
            )

    # 处理所有标题
    def adjust_heading(match):
        heading = match.group(1)
        content = match.group(2)
        level = len(heading)
        new_level = max(min(level + adjustment, max_level), min_level)
        return "#" * new_level + " " + content

    processed_content = heading_pattern.sub(adjust_heading, markdown_content)

    # 恢复第一个一级标题（如果需要）
    if first_level_as_title and first_h1:
        processed_content = processed_content.replace(
            "%%FIRST_TITLE_PLACEHOLDER%%", first_h1
        )

    return processed_content


def extract_heading_structure(
    markdown_content: str,
) -> List[Dict[str, Union[int, str]]]:
    """
    提取Markdown文档中的标题结构

    参数:
        markdown_content: Markdown内容

    返回:
        标题结构列表，每个标题包含级别和内容
    """
    heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

    headings = []
    for match in heading_pattern.finditer(markdown_content):
        level = len(match.group(1))
        content = match.group(2)
        headings.append(
            {"level": level, "content": content, "original": match.group(0)}
        )

    return headings


def apply_custom_heading_levels(
    markdown_content: str, level_mapping: Dict[int, int]
) -> str:
    """
    根据自定义映射调整Markdown标题层级

    参数:
        markdown_content: 要处理的Markdown内容
        level_mapping: 层级映射字典，如 {1: 2, 2: 3} 表示将一级标题变为二级，二级变为三级

    返回:
        处理后的Markdown内容
    """
    heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

    def map_heading(match):
        heading = match.group(1)
        content = match.group(2)
        level = len(heading)

        if level in level_mapping:
            new_level = max(min(level_mapping[level], 6), 1)
            return "#" * new_level + " " + content

        return match.group(0)

    return heading_pattern.sub(map_heading, markdown_content)


def convert_lists_to_headings(markdown_content: str) -> str:
    """
    将Markdown文档中带有加粗小标题的无序列表转换为多级标题

    处理以下格式的无序列表项:
    - **小标题:** 内容文本

    转换为:
    ### 小标题
    内容文本

    参数:
        markdown_content: 要处理的Markdown内容

    返回:
        处理后的Markdown内容
    """
    # 分割文本为行
    lines = markdown_content.split("\n")
    result_lines = []
    current_heading_level = 0

    # 遍历每一行
    for i, line in enumerate(lines):
        # 匹配标题行来确定当前标题级别
        heading_match = re.match(r"^(#{1,6})\s+", line)
        if heading_match:
            current_heading_level = len(heading_match.group(1))

        # 匹配带有加粗小标题的无序列表
        list_match = re.match(r"^(\s*)-\s+\*\*(.*?):\*\*\s*(.*?)$", line)
        if list_match:
            indent = list_match.group(1)
            title = list_match.group(2).strip()
            content = list_match.group(3).strip()

            # 创建新的标题行（当前标题级别+1）
            new_heading_level = min(current_heading_level + 1, 6)
            heading_line = indent + "#" * new_heading_level + " " + title

            # 添加转换后的内容
            result_lines.append(heading_line)
            if content:  # 如果有内容文本，添加为正文
                result_lines.append(indent + content)
        else:
            # 匹配另一种格式: - **小标题** 内容文本
            alt_list_match = re.match(r"^(\s*)-\s+\*\*(.*?)\*\*\s*(.*?)$", line)
            if alt_list_match:
                indent = alt_list_match.group(1)
                title = alt_list_match.group(2).strip()
                content = alt_list_match.group(3).strip()

                # 创建新的标题行（当前标题级别+1）
                new_heading_level = min(current_heading_level + 1, 6)
                heading_line = indent + "#" * new_heading_level + " " + title

                # 添加转换后的内容
                result_lines.append(heading_line)
                if content:  # 如果有内容文本，添加为正文
                    result_lines.append(indent + content)
            else:
                result_lines.append(line)

    return "\n".join(result_lines)
