"""
加载ChatGPT导出的JSON对话数据
"""

import json
from typing import Dict, Any
from ..logger import setup_logger
from ..models import Conversation

# 配置日志
logger = setup_logger(name=__name__)


def load_conversation(file_path: str) -> Conversation:
    """
    从文件加载ChatGPT对话数据

    Args:
        file_path: JSON文件路径

    Returns:
        解析后的对话数据对象

    Raises:
        FileNotFoundError: 文件不存在
        json.JSONDecodeError: JSON解析错误
        ValueError: 数据格式错误
    """
    logger.debug(f"加载对话文件: {file_path}")
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析错误: {str(e)}")
                raise ValueError(f"文件不是有效的JSON格式: {str(e)}")

        logger.debug("对话文件加载成功")

        # 基本格式验证
        if not isinstance(data, dict):
            logger.error("对话数据不是字典格式")
            raise ValueError("无效的对话数据格式：期望一个JSON对象，但得到了其他类型")

        # 检查必要的字段
        required_fields = ["mapping", "title"]
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            logger.error(f"对话数据缺少必要字段: {missing_fields}")
            raise ValueError(
                f"对话数据格式不完整，缺少字段: {', '.join(missing_fields)}"
            )

        # 将字典转换为Conversation对象
        try:
            conversation = Conversation.model_validate(data)
            logger.debug(f"成功解析对话数据，包含 {len(conversation.mapping)} 个节点")
            return conversation
        except Exception as e:
            logger.error(f"解析对话数据失败: {str(e)}")
            raise ValueError(f"无法解析对话数据: {str(e)}")

    except FileNotFoundError:
        logger.error(f"文件不存在: {file_path}")
        raise FileNotFoundError(f"文件不存在: {file_path}")
    except Exception as e:
        if not isinstance(e, (ValueError, FileNotFoundError)):
            logger.error(f"加载对话文件时发生未知错误: {str(e)}")
            raise ValueError(f"加载对话数据时发生错误: {str(e)}")
        raise
