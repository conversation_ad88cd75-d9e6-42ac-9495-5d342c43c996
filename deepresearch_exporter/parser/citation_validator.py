"""
引用验证模块，用于检查 Markdown 文件中的引用是否在 BibTeX 文件中存在
"""

import re
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
import difflib

from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def load_markdown_file(markdown_path: str) -> str:
    """
    加载 Markdown 文件内容

    Args:
        markdown_path: Markdown 文件路径

    Returns:
        Markdown 文件内容
    """
    try:
        with open(markdown_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"读取 Markdown 文件失败: {str(e)}")
        raise


def extract_markdown_citations(markdown_text: str) -> Set[str]:
    """
    从 Markdown 文本中提取所有引用键

    Args:
        markdown_text: Markdown 文本内容

    Returns:
        Markdown 文本中的引用键集合
    """
    # 查找 Markdown 文本中的所有引用标记，格式为 [@citekey]
    cite_pattern = r"\[@([^\]]+)\]"
    cite_matches = re.finditer(cite_pattern, markdown_text)

    # 提取所有引用键
    cite_keys = {match.group(1) for match in cite_matches}

    return cite_keys


def load_bibtex_keys(bibtex_path: str) -> Set[str]:
    """
    从 BibTeX 文件中加载所有引用键

    Args:
        bibtex_path: BibTeX 文件路径

    Returns:
        BibTeX 文件中的引用键集合
    """
    try:
        with open(bibtex_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 匹配 BibTeX 条目的正则表达式
        # 匹配 @类型{键, 的格式
        entry_pattern = r"@\w+\s*{\s*([^,]+)"
        entry_matches = re.finditer(entry_pattern, content)

        # 提取所有引用键
        bibtex_keys = {match.group(1).strip() for match in entry_matches}

        return bibtex_keys
    except Exception as e:
        logger.error(f"解析 BibTeX 文件失败: {str(e)}")
        return set()


def find_similar_keys(
    missing_key: str, available_keys: Set[str], max_suggestions: int = 3
) -> List[str]:
    """
    为缺失的引用键查找相似的可用引用键

    Args:
        missing_key: 缺失的引用键
        available_keys: 可用的引用键集合
        max_suggestions: 最大建议数量

    Returns:
        相似引用键列表
    """
    if not available_keys:
        return []

    # 使用 difflib 计算字符串相似度
    similarities = []
    for key in available_keys:
        ratio = difflib.SequenceMatcher(None, missing_key, key).ratio()
        similarities.append((key, ratio))

    # 按相似度排序并选取前N个
    similarities.sort(key=lambda x: x[1], reverse=True)

    # 只返回相似度超过阈值的建议
    threshold = 0.5  # 相似度阈值
    similar_keys = [
        key for key, ratio in similarities[:max_suggestions] if ratio > threshold
    ]

    return similar_keys


def validate_markdown_citations_without_highlight(
    markdown_path: str, bibtex_path: str
) -> Tuple[bool, List[str], Dict[str, List[str]]]:
    """
    验证 Markdown 文件中的引用是否在 BibTeX 文件中存在，不添加高亮标记

    Args:
        markdown_path: Markdown 文件路径
        bibtex_path: BibTeX 文件路径

    Returns:
        验证结果元组 (是否全部匹配, 缺失的引用键列表, 相似引用键建议字典)
    """
    # 加载 Markdown 文件
    markdown_text = load_markdown_file(markdown_path)

    # 提取 Markdown 中的所有引用键
    markdown_cite_keys = extract_markdown_citations(markdown_text)

    # 如果没有引用，直接返回成功
    if not markdown_cite_keys:
        logger.info("Markdown 文件中未找到引用")
        return True, [], {}

    # 加载 BibTeX 文件中的所有引用键
    bibtex_cite_keys = load_bibtex_keys(bibtex_path)

    # 查找缺失的引用键
    missing_keys = [key for key in markdown_cite_keys if key not in bibtex_cite_keys]

    # 为每个缺失的键查找相似的引用建议
    suggestions = {}
    for key in missing_keys:
        similar_keys = find_similar_keys(key, bibtex_cite_keys)
        if similar_keys:
            suggestions[key] = similar_keys

    is_all_valid = len(missing_keys) == 0

    return is_all_valid, missing_keys, suggestions


def highlight_missing_citations(
    markdown_path: str, bibtex_path: str, output_path: str
) -> Tuple[bool, List[str], str, Dict[str, List[str]]]:
    """
    高亮 Markdown 文件中缺失的引用，并输出到新文件

    Args:
        markdown_path: Markdown 文件路径
        bibtex_path: BibTeX 文件路径
        output_path: 输出文件路径

    Returns:
        验证结果元组 (是否全部匹配, 缺失的引用键列表, 输出文件路径, 相似引用键建议字典)
    """
    # 验证引用
    is_all_valid, missing_keys, highlighted_text, suggestions = (
        validate_markdown_citations(markdown_path, bibtex_path)
    )

    # 如果有输出路径，保存高亮后的文本
    if output_path:
        # 确保输出目录存在
        output_dir = Path(output_path).parent
        output_dir.mkdir(exist_ok=True, parents=True)

        # 写入高亮后的文本
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(highlighted_text)

    # 确保返回的路径是字符串，而不是 Path 对象
    return is_all_valid, missing_keys, str(output_path), suggestions


def generate_missing_citation_report(
    missing_keys: List[str], suggestions: Dict[str, List[str]] = None
) -> str:
    """
    生成缺失引用的报告

    Args:
        missing_keys: 缺失的引用键列表
        suggestions: 相似引用键建议字典

    Returns:
        缺失引用的报告文本
    """
    if not missing_keys:
        return "✅ 所有引用均在 BibTeX 文件中找到，引用验证通过！"

    report_lines = ["# 引用验证报告", ""]
    report_lines.append(f"❌ 发现 {len(missing_keys)} 个引用在 BibTeX 文件中缺失:")
    report_lines.append("")

    suggestions = suggestions or {}

    for key in missing_keys:
        report_line = f"- `[@{key}]`"
        if key in suggestions and suggestions[key]:
            report_line += (
                f" 可能是指: {', '.join(['`[@' + s + ']`' for s in suggestions[key]])}"
            )
        report_lines.append(report_line)

    report_lines.append("")
    report_lines.append(
        "请检查这些引用键是否正确，或者在 BibTeX 文件中添加相应的条目。"
    )

    return "\n".join(report_lines)


def replace_citation(markdown_text: str, old_key: str, new_key: str) -> str:
    """
    替换 Markdown 中的引用键

    Args:
        markdown_text: Markdown 文本
        old_key: 原引用键
        new_key: 新引用键

    Returns:
        替换后的 Markdown 文本
    """
    # 直接替换引用键
    pattern = r"\[@" + re.escape(old_key) + r"\]"
    replaced_text = re.sub(pattern, f"[@{new_key}]", markdown_text)

    # 替换可能存在的高亮标记的缺失引用（更强大的模式匹配）
    # 这会匹配[![@key] - 引用未找到!]形式和更复杂的格式
    highlight_pattern = r"\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\*"
    replaced_text = re.sub(highlight_pattern, f"[@{new_key}]", replaced_text)

    # 尝试匹配其他可能的高亮格式
    alt_pattern = r"\*\*\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\*\*\*"
    replaced_text = re.sub(alt_pattern, f"[@{new_key}]", replaced_text)

    # 匹配可能嵌套的高亮格式
    nested_pattern = r"\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\* - [^\]]+\]"
    replaced_text = re.sub(nested_pattern, f"[@{new_key}]", replaced_text)

    return replaced_text


def replace_citations_in_file(
    markdown_path: str, replacements: Dict[str, str], output_path: Optional[str] = None
) -> str:
    """
    在文件中批量替换引用键

    Args:
        markdown_path: Markdown 文件路径
        replacements: 替换字典 {原键: 新键}
        output_path: 输出文件路径

    Returns:
        输出文件路径
    """
    # 读取原文件
    with open(markdown_path, "r", encoding="utf-8") as f:
        markdown_text = f.read()

    # 逐个应用替换
    for old_key, new_key in replacements.items():
        markdown_text = replace_citation(markdown_text, old_key, new_key)

    # 如果没有指定输出路径，则覆盖原文件
    if output_path is None:
        output_path = markdown_path

    # 保存更新后的文件
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(markdown_text)

    return output_path
