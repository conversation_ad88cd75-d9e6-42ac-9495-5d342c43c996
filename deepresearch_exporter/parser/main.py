"""
主解析模块，协调各解析组件处理对话
"""

import sys
import os
from typing import Literal
from ..models import DeepResearchResponse
from ..logger import setup_logger
from .loader import load_conversation
from .response_extractor import extract_deep_research_content

# 配置日志
logger = setup_logger(name=__name__)


def process_conversation(
    file_path: str, mode: Literal["citations", "content_references"] = "citations"
) -> DeepResearchResponse:
    """
    处理ChatGPT Deep Research对话，提取内容和引用

    Args:
        file_path: 对话JSON文件路径
        mode: 处理模式，'citations'使用引用标记处理，'content_references'使用内容引用处理

    Returns:
        处理后的Deep Research响应对象
    """
    try:
        # 检查文件路径是否有效
        if not file_path or not os.path.exists(file_path):
            logger.error(f"文件路径无效或不存在: {file_path}")
            raise FileNotFoundError(f"文件路径无效或不存在: {file_path}")

        # 加载对话
        conversation = load_conversation(file_path)

        # 提取Deep Research内容
        response = extract_deep_research_content(conversation, mode=mode)

        if not response:
            logger.warning("未找到Deep Research内容，返回空响应")
            return DeepResearchResponse(content="", citations=[])

        logger.info(f"成功提取Deep Research内容，包含 {len(response.citations)} 条引用")
        return response

    except Exception as e:
        logger.error(f"处理对话失败: {str(e)}", exc_info=True)
        raise
