"""
检测器模块，用于识别Deep Research模式的消息
"""

from ..models import Message
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def is_deep_research_message(message: Message) -> bool:
    """
    判断消息是否为Deep Research模式的回复

    Args:
        message: 消息对象

    Returns:
        是否为Deep Research模式
    """
    # 检查消息元数据中是否包含citations或content_references字段，这是Deep Research模式的特征
    if message.metadata and (
        message.metadata.get("citations") or message.metadata.get("content_references")
    ):
        logger.debug("检测到消息包含citations或content_references字段")
        return True

    # 检查消息元数据中是否有research模型标记
    if message.metadata and message.metadata.get("model_slug") == "research":
        logger.debug("检测到消息使用research模型")
        return True

    # 检查是否有is_async_task_result_message标记，这通常表示Deep Research结果
    if message.metadata and message.metadata.get("is_async_task_result_message"):
        logger.debug("检测到消息是异步任务结果")
        return True

    return False
