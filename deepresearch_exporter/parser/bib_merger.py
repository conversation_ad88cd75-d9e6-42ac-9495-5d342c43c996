"""
BibTeX和Markdown整合模块，用于将BibTeX引用信息直接整合到Markdown中
"""

import re
from typing import Dict, List, Optional, Tuple, Set
from pathlib import Path

from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def load_bibtex(bib_path: str) -> Dict[str, Dict]:
    """
    加载BibTeX文件并解析为字典

    Args:
        bib_path: BibTeX文件路径

    Returns:
        包含引用信息的字典，键为citekey
    """
    try:
        # 直接读取BibTeX文件
        with open(bib_path, "r", encoding="utf-8") as f:
            content = f.read()

        entries = {}

        # 使用状态机方法解析BibTeX，以处理URL中可能包含@符号的情况
        lines = content.splitlines()
        current_entry = None
        current_key = None
        current_type = None
        current_content = []
        in_entry = False
        brace_count = 0

        for line in lines:
            line_stripped = line.strip()

            # 检测条目开始
            if not in_entry and line_stripped.startswith("@"):
                # 匹配条目类型和键
                entry_start = re.match(r"@(\w+)\s*{\s*([^,]*)", line_stripped)
                if entry_start:
                    in_entry = True
                    current_type = entry_start.group(1).lower()
                    current_key = entry_start.group(2).strip()
                    brace_count = line_stripped.count("{") - line_stripped.count("}")

                    # 收集内容，但跳过类型和键的部分
                    content_start = line_stripped.find(",")
                    if content_start > 0:
                        # 如果逗号在当前行，添加逗号后的内容
                        current_content.append(line_stripped[content_start + 1 :])

                    logger.debug(f"开始解析条目: 类型={current_type}, 键={current_key}")
                    continue

            # 在条目内
            if in_entry:
                # 更新大括号计数
                brace_count += line_stripped.count("{") - line_stripped.count("}")

                # 收集内容
                current_content.append(line_stripped)

                # 检测条目结束
                if brace_count <= 0 and line_stripped.rstrip().endswith("}"):
                    # 条目解析结束
                    in_entry = False

                    # 去除最后一行的结束大括号
                    last_line = current_content[-1]
                    if last_line.rstrip().endswith("}"):
                        current_content[-1] = last_line[
                            : last_line.rstrip().rfind("}")
                        ].rstrip()

                    entry_content = "\n".join(current_content)

                    # 创建条目
                    entry = {
                        "key": current_key,
                        "type": current_type,
                        "authors": [],
                        "title": "",
                        "year": "",
                        "url": "",
                        "doi": "",
                        "journal": "",
                    }

                    # 提取字段
                    # 处理可能的多行字段
                    field_pattern = r'(\w+)\s*=\s*(?:{(.*?)}|"(.*?)"|([^,\s]+))'

                    for field_match in re.finditer(
                        field_pattern, entry_content, re.DOTALL
                    ):
                        field_name = field_match.group(1).lower()
                        # 获取非None的第一个捕获组作为字段值
                        field_value = next(
                            (g for g in field_match.groups()[1:] if g is not None), ""
                        ).strip()

                        # 清理值中的引号和花括号
                        field_value = field_value.strip('"{},')

                        if field_name == "url":
                            entry["url"] = field_value
                            logger.debug(f"提取URL: {field_value}")
                        elif field_name == "title":
                            entry["title"] = field_value
                        elif field_name == "author":
                            # 拆分作者
                            authors = [
                                a.strip() for a in re.split(r"\s+and\s+", field_value)
                            ]
                            entry["authors"] = authors
                        elif field_name == "year":
                            # 尝试提取年份（通常是4位数字）
                            year_match = re.search(r"(\d{4})", field_value)
                            if year_match:
                                entry["year"] = year_match.group(1)
                            else:
                                entry["year"] = field_value
                        elif field_name == "doi":
                            entry["doi"] = field_value
                            # 如果没有URL但有DOI，使用DOI作为URL
                            if not entry.get("url") and field_value:
                                entry["url"] = f"https://doi.org/{field_value}"
                        elif field_name == "journal":
                            entry["journal"] = field_value

                    # URL特殊处理
                    if not entry.get("url"):
                        # 分析各个字段，查找可能包含URL的字段
                        for field_name, field_value in re.findall(
                            r"(\w+)\s*=\s*{([^}]*)}", entry_content
                        ):
                            if (
                                field_name.lower() == "url"
                                or "url" in field_name.lower()
                                or "link" in field_name.lower()
                            ):
                                url = field_value.strip()
                                if url:
                                    entry["url"] = url
                                    logger.debug(f"从字段 {field_name} 提取URL: {url}")
                                    break

                        # 如果仍未找到URL，尝试直接从内容中提取
                        if not entry.get("url"):
                            # 正则匹配http开头的URL
                            http_urls = list(
                                re.finditer(r'https?://[^\s,}"<>]+', entry_content)
                            )
                            if http_urls:
                                url = http_urls[0].group(0)
                                url = url.rstrip(".,;}])'\"")
                                entry["url"] = url
                                logger.debug(f"从未完成条目提取HTTP URL: {url}")
                            else:
                                # 查找可能是URL的文本
                                www_urls = list(
                                    re.finditer(
                                        r'www\.[^\s,}"<>]+\.[a-z]{2,}[^\s,}"<>]*',
                                        entry_content,
                                    )
                                )
                                if www_urls:
                                    url = www_urls[0].group(0)
                                    url = url.rstrip(".,;}])'\"")
                                    if not url.startswith(("http://", "https://")):
                                        url = "https://" + url
                                    entry["url"] = url
                                    logger.debug(f"从未完成条目提取WWW URL: {url}")

                        # 特殊处理threads.com URL，可能包含@符号
                        if not entry.get("url") or "threads.com" in entry.get(
                            "url", ""
                        ):
                            # 首先尝试匹配完整的Threads URL模式
                            threads_pattern = (
                                r'https?://(?:www\.)?threads\.com/@[^\s,}"]*'
                            )
                            threads_urls = list(
                                re.finditer(threads_pattern, entry_content)
                            )
                            if threads_urls:
                                url = threads_urls[0].group(0)
                                url = url.rstrip(".,;}])'\"")
                                entry["url"] = url
                                logger.debug(f"从内容中提取完整Threads URL: {url}")
                            else:
                                # 如果找不到完整模式，尝试分段匹配
                                seg_pattern = (
                                    r'(https?://(?:www\.)?threads\.com/[^\s,}"]*)'
                                )
                                seg_urls = list(re.finditer(seg_pattern, entry_content))
                                if seg_urls:
                                    url = seg_urls[0].group(1)
                                    url = url.rstrip(".,;}])'\"")
                                    entry["url"] = url
                                    logger.debug(f"从内容中提取部分Threads URL: {url}")
                                else:
                                    # 最后尝试任何包含threads.com的字符串
                                    any_threads = list(
                                        re.finditer(
                                            r"(https?://[^\s]*threads\.com[^\s]*)",
                                            entry_content,
                                        )
                                    )
                                    if any_threads:
                                        url = any_threads[0].group(1)
                                        url = url.rstrip(".,;}])'\"")
                                        entry["url"] = url
                                        logger.debug(
                                            f"从内容中提取任何Threads URL: {url}"
                                        )

                    # 存储解析后的条目
                    entries[current_key] = entry
                    logger.debug(f"成功解析条目: {current_key}")

                    # 重置状态
                    current_content = []
                    current_key = None
                    current_type = None

        # 处理可能的未完成条目
        if in_entry and current_key:
            logger.warning(f"检测到未正确关闭的条目: {current_key}")

            # 尝试处理不完整条目
            entry_content = "\n".join(current_content)

            # 创建条目
            entry = {
                "key": current_key,
                "type": current_type,
                "authors": [],
                "title": "",
                "year": "",
                "url": "",
                "doi": "",
                "journal": "",
            }

            # 提取字段 - 同上
            field_pattern = r'(\w+)\s*=\s*(?:{(.*?)}|"(.*?)"|([^,\s]+))'

            for field_match in re.finditer(field_pattern, entry_content, re.DOTALL):
                field_name = field_match.group(1).lower()
                field_value = next(
                    (g for g in field_match.groups()[1:] if g is not None), ""
                ).strip()
                field_value = field_value.strip('"{},')

                # 同上处理各字段
                if field_name == "url":
                    entry["url"] = field_value
                elif field_name == "title":
                    entry["title"] = field_value
                elif field_name == "author":
                    authors = [a.strip() for a in re.split(r"\s+and\s+", field_value)]
                    entry["authors"] = authors
                elif field_name == "year":
                    year_match = re.search(r"(\d{4})", field_value)
                    if year_match:
                        entry["year"] = year_match.group(1)
                    else:
                        entry["year"] = field_value
                elif field_name == "doi":
                    entry["doi"] = field_value
                    if not entry.get("url") and field_value:
                        entry["url"] = f"https://doi.org/{field_value}"
                elif field_name == "journal":
                    entry["journal"] = field_value

            # URL特殊处理
            if not entry.get("url"):
                # 分析各个字段，查找可能包含URL的字段
                for field_name, field_value in re.findall(
                    r"(\w+)\s*=\s*{([^}]*)}", entry_content
                ):
                    if (
                        field_name.lower() == "url"
                        or "url" in field_name.lower()
                        or "link" in field_name.lower()
                    ):
                        url = field_value.strip()
                        if url:
                            entry["url"] = url
                            logger.debug(f"从字段 {field_name} 提取URL: {url}")
                            break

                # 如果仍未找到URL，尝试直接从内容中提取
                if not entry.get("url"):
                    # 正则匹配http开头的URL
                    http_urls = list(
                        re.finditer(r'https?://[^\s,}"<>]+', entry_content)
                    )
                    if http_urls:
                        url = http_urls[0].group(0)
                        url = url.rstrip(".,;}])'\"")
                        entry["url"] = url
                        logger.debug(f"从未完成条目提取HTTP URL: {url}")
                    else:
                        # 查找可能是URL的文本
                        www_urls = list(
                            re.finditer(
                                r'www\.[^\s,}"<>]+\.[a-z]{2,}[^\s,}"<>]*', entry_content
                            )
                        )
                        if www_urls:
                            url = www_urls[0].group(0)
                            url = url.rstrip(".,;}])'\"")
                            if not url.startswith(("http://", "https://")):
                                url = "https://" + url
                            entry["url"] = url
                            logger.debug(f"从未完成条目提取WWW URL: {url}")

                # 特殊处理threads.com URL，可能包含@符号
                if not entry.get("url") or "threads.com" in entry.get("url", ""):
                    # 首先尝试匹配完整的Threads URL模式
                    threads_pattern = r'https?://(?:www\.)?threads\.com/@[^\s,}"]*'
                    threads_urls = list(re.finditer(threads_pattern, entry_content))
                    if threads_urls:
                        url = threads_urls[0].group(0)
                        url = url.rstrip(".,;}])'\"")
                        entry["url"] = url
                        logger.debug(f"从内容中提取完整Threads URL: {url}")
                    else:
                        # 如果找不到完整模式，尝试分段匹配
                        seg_pattern = r'(https?://(?:www\.)?threads\.com/[^\s,}"]*)'
                        seg_urls = list(re.finditer(seg_pattern, entry_content))
                        if seg_urls:
                            url = seg_urls[0].group(1)
                            url = url.rstrip(".,;}])'\"")
                            entry["url"] = url
                            logger.debug(f"从内容中提取部分Threads URL: {url}")
                        else:
                            # 最后尝试任何包含threads.com的字符串
                            any_threads = list(
                                re.finditer(
                                    r"(https?://[^\s]*threads\.com[^\s]*)",
                                    entry_content,
                                )
                            )
                            if any_threads:
                                url = any_threads[0].group(1)
                                url = url.rstrip(".,;}])'\"")
                                entry["url"] = url
                                logger.debug(f"从内容中提取任何Threads URL: {url}")

            # 存储解析后的条目
            entries[current_key] = entry

        logger.info(f"成功解析 {len(entries)} 个BibTeX条目")
        return entries
    except Exception as e:
        logger.error(f"加载BibTeX文件失败: {str(e)}")
        return {}


def replace_citations_with_links(
    markdown_text: str,
    citations: Dict[str, Dict],
    url_deduplicate: bool = False,  # 设置为True时，相同URL的引用将使用相同的序号，保证参考文献不会重复
    complete_deduplicate: bool = False,  # 设置为True时，完全去重，同一URL的多个相邻引用将合并为一个
    link_text_pattern: str = "[{index}]",
) -> Tuple[str, Dict[str, Dict]]:
    """
    将Markdown中的引用标记替换为链接

    Args:
        markdown_text: Markdown文本内容
        citations: 引用信息字典，键为citekey
        url_deduplicate: 是否对URL进行去重
        complete_deduplicate: 是否完全去重（合并相邻的相同URL引用）
        link_text_pattern: 链接文本模式，可使用{index}作为序号占位符

    Returns:
        处理后的Markdown文本和使用到的引用字典

    注意:
        当url_deduplicate=True时，如果多个引用使用相同的URL，它们将被分配相同的索引号。
        这确保了参考文献部分生成时，相同URL的引用只会出现一次，并且文中的引用编号与参考文献列表中的编号一致。
        
        当complete_deduplicate=True时，相邻的相同URL引用将被合并为一个引用，避免出现连续的相同引用标签。
    """
    # 查找Markdown文本中的所有引用标记，格式为[@citekey]
    # 修改正则表达式以支持任意字符作为citekey
    cite_pattern = r"\[@([^\]]+)\]"
    cite_matches = list(re.finditer(cite_pattern, markdown_text))

    # 记录已处理过的URL，用于去重
    processed_urls = {}
    url_counter = 1  # 为所有URL编号

    # 记录使用到的引用
    used_citations = {}

    # 首先收集所有引用信息和URL对应关系
    url_to_citekeys = {}  # 跟踪相同URL对应的多个citekey
    citekey_to_index = {}  # 保存每个citekey对应的索引号
    citekey_to_base_url = {}  # 保存每个citekey对应的基础URL

    # 第一遍遍历，收集URL和citekey的对应关系
    for match in cite_matches:
        citekey = match.group(1)

        # 检查citekey是否在引用字典中
        if citekey in citations:
            citation = citations[citekey]
            url = citation.get("url", "")

            # 如果没有URL，跳过处理
            if not url:
                logger.warning(f"引用 {citekey} 没有URL，将保持原样")
                continue

            # URL去重预处理
            if url_deduplicate:
                # 提取URL的主要部分（去除查询参数等）
                base_url = re.sub(r"([?#].*)$", "", url)
                
                # 记录citekey与base_url的关系（用于完全去重）
                citekey_to_base_url[citekey] = base_url

                # 收集URL到citekey的映射
                if base_url not in url_to_citekeys:
                    url_to_citekeys[base_url] = []
                if citekey not in url_to_citekeys[base_url]:
                    url_to_citekeys[base_url].append(citekey)
            else:
                # 非去重模式下，每个citekey都是独立的
                base_url = f"{url}#{citekey}"  # 确保唯一
                url_to_citekeys[base_url] = [citekey]
                citekey_to_base_url[citekey] = base_url

    # 为URL分配索引号
    for base_url, citekeys in url_to_citekeys.items():
        index = url_counter
        url_counter += 1
        processed_urls[base_url] = index

        # 将索引号分配给每个citekey
        for citekey in citekeys:
            citekey_to_index[citekey] = index

    # 收集替换信息
    replacements = []
    last_replaced_position = -1
    last_base_url = None
    
    # 按照文档中出现顺序处理引用（从前到后）
    sorted_matches = sorted(cite_matches, key=lambda m: m.start())
    
    for match in sorted_matches:
        citekey = match.group(1)
        start_pos = match.start()
        end_pos = match.end()

        # 检查citekey是否已分配索引号
        if citekey in citekey_to_index:
            citation = citations[citekey]
            url = citation.get("url", "")
            index = citekey_to_index[citekey]
            base_url = citekey_to_base_url.get(citekey)

            # 处理完全去重逻辑
            if complete_deduplicate and url_deduplicate:
                # 如果当前引用与上一个引用紧邻且基础URL相同，则跳过当前引用
                if last_replaced_position >= 0 and last_base_url == base_url:
                    # 仅当两个引用之间没有实质内容时才合并
                    between_text = markdown_text[last_replaced_position:start_pos].strip()
                    if not between_text or re.match(r'^[\s,.;:!?]*$', between_text):
                        # 将当前引用替换为空字符串
                        replacements.append((start_pos, end_pos, ""))
                        continue
            
            # 格式化链接文本
            link_text = link_text_pattern.format(index=index)
            # 创建Markdown链接
            link = f"[{link_text}]({url})"

            # 保存替换信息
            replacements.append((start_pos, end_pos, link))
            
            # 更新上一个被替换的位置和URL
            last_replaced_position = end_pos
            last_base_url = base_url

            # 记录使用到的引用，并添加索引信息
            citation_copy = citation.copy()
            citation_copy["index"] = index
            used_citations[citekey] = citation_copy

    # 从后向前替换，避免位置偏移
    for start_pos, end_pos, link in sorted(replacements, reverse=True):
        markdown_text = markdown_text[:start_pos] + link + markdown_text[end_pos:]

    return markdown_text, used_citations


def generate_references_section(used_citations: Dict[str, Dict]) -> str:
    """
    生成参考文献章节

    Args:
        used_citations: 使用到的引用字典，键为citekey

    Returns:
        参考文献章节的Markdown文本

    注意:
        此函数能够处理URL去重的情况，通过按索引号分组引用，确保文中引用的编号与参考文献列表中的编号一致。
        当多个引用共用同一个索引号时（URL去重模式），仅选择其中一个显示在参考文献列表中。
    """
    if not used_citations:
        return ""

    # 创建按索引号排序的字典，处理可能有URL去重导致的索引号不连续问题
    index_to_citations = {}
    for citation in used_citations.values():
        index = citation.get("index", 0)
        if index not in index_to_citations:
            index_to_citations[index] = []
        index_to_citations[index].append(citation)

    # 按索引号排序
    sorted_indexes = sorted(index_to_citations.keys())

    # 生成参考文献列表
    references = ["# 参考文献", ""]

    for index in sorted_indexes:
        citations_for_index = index_to_citations[index]

        # 对于每个索引，仅使用第一个引用（URL去重模式下多个引用共享相同URL）
        # 选择作者字母顺序最靠前的那个引用
        citations_for_index.sort(key=lambda x: "".join(x.get("authors", [""])))
        # 只取排序后的第一个引用
        citation = citations_for_index[0]

        authors = ", ".join(citation.get("authors", []))
        title = citation.get("title", "")
        year = citation.get("year", "")
        journal = citation.get("journal", "")
        url = citation.get("url", "")

        # 去除URL中的查询参数和片段标识符
        if url:
            url = re.sub(r"([?#].*)$", "", url)

        # 构建引用文本
        ref_text = f"{index}. "
        # if authors:
        #     ref_text += f"{authors}. "
        if title:
            ref_text += f"*{title}*. "
        if journal:
            ref_text += f"{journal}, "
        if year:
            ref_text += f"{year}. "
        if url:
            ref_text += f"[链接]({url})"

        references.append(ref_text)

    return "\n".join(references)


def merge_markdown_with_bibtex(
    markdown_path: str,
    bibtex_path: str,
    output_path: str,
    url_deduplicate: bool = False,  # 设置为True时，相同URL的引用将使用相同的序号，保证参考文献不会重复
    complete_deduplicate: bool = False,  # 设置为True时，完全去重，合并相邻的相同URL引用
    link_text_pattern: str = "[{index}]",
    append_references: bool = False,
) -> str:
    """
    将Markdown文件与BibTeX引用整合

    Args:
        markdown_path: Markdown文件路径
        bibtex_path: BibTeX文件路径
        output_path: 输出文件路径
        url_deduplicate: 是否对URL进行去重
        complete_deduplicate: 是否完全去重（合并相邻的相同URL引用）
        link_text_pattern: 链接文本模式，可使用{index}作为序号占位符
        append_references: 是否在文档末尾添加参考文献章节

    Returns:
        输出文件路径
    """
    # 加载BibTeX
    citations = load_bibtex(bibtex_path)

    # 读取Markdown文件
    with open(markdown_path, "r", encoding="utf-8") as f:
        markdown_text = f.read()

    # 处理引用替换
    processed_text, used_citations = replace_citations_with_links(
        markdown_text,
        citations,
        url_deduplicate=url_deduplicate,
        complete_deduplicate=complete_deduplicate,
        link_text_pattern=link_text_pattern,
    )

    # 如果选择添加参考文献章节
    if append_references and used_citations:
        references_section = generate_references_section(used_citations)
        # 在文档末尾添加参考文献章节
        processed_text += "\n\n" + references_section

    # 确保输出目录存在
    output_dir = Path(output_path).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    # 写入输出文件
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(processed_text)

    return output_path
