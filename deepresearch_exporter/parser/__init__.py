"""
向后兼容的parser模块
为了保持向后兼容性，这个模块重新导出了重构后的功能
"""

import warnings
from typing import Dict, List, Any, Tuple, Optional, Set, Union

# 导入重构后的功能
from ..core.processor import ConversationProcessor
from ..core.validator import CitationValidator
from ..tools.markdown import MarkdownProcessor
from ..tools.bibtex import BibtexProcessor

# 发出弃用警告
warnings.warn(
    "The 'parser' module is deprecated and will be removed in a future version. "
    "Please use the new modular structure: core.processor, core.validator, "
    "tools.markdown, and tools.bibtex instead.",
    DeprecationWarning,
    stacklevel=2
)

# 创建全局实例
_processor = ConversationProcessor()
_validator = CitationValidator()
_markdown = MarkdownProcessor()
_bibtex = BibtexProcessor()

# 向后兼容的函数导出

# loader.py 兼容性
def load_conversation(file_path: str):
    """向后兼容：加载对话文件"""
    return _processor.load_conversation(file_path)

# detector.py 兼容性
def is_deep_research_message(message):
    """向后兼容：检测Deep Research消息"""
    return _processor.is_deep_research_message(message)

# response_extractor.py 兼容性
def extract_deep_research_content(conversation, mode: str = "citations"):
    """向后兼容：提取Deep Research内容"""
    return _processor.extract_deep_research_content(conversation, mode)

# content_processor.py 兼容性
def process_content_with_direct_references(message, citations_data: List[Dict]):
    """向后兼容：处理直接引用内容"""
    return _processor.process_content_with_direct_references(message, citations_data)

def process_content_with_content_references(message, content_references: List[Dict]):
    """向后兼容：处理内容引用"""
    return _processor.process_content_with_content_references(message, content_references)

# main.py 兼容性
def process_conversation(file_path: str, mode: str = "citations"):
    """向后兼容：处理对话"""
    return _processor.process_conversation(file_path, mode)

# citation_validator.py 兼容性
def validate_markdown_citations(markdown_path: str, bibtex_path: str):
    """向后兼容：验证Markdown引用"""
    return _validator.validate_citations(markdown_path, bibtex_path)

def highlight_missing_citations(markdown_path: str, bibtex_path: str, output_path: str):
    """向后兼容：高亮缺失引用"""
    return _validator.highlight_missing_citations(markdown_path, bibtex_path, output_path)

def generate_missing_citation_report(missing_keys: List[str], suggestions: Dict[str, List[str]] = None):
    """向后兼容：生成缺失引用报告"""
    return _validator.generate_report(missing_keys, suggestions)

def replace_citation(markdown_text: str, old_key: str, new_key: str):
    """向后兼容：替换引用键"""
    return _validator.replace_citation(markdown_text, old_key, new_key)

def replace_citations_in_file(markdown_path: str, replacements: Dict[str, str], output_path: str = None):
    """向后兼容：在文件中批量替换引用键"""
    return _validator.replace_citations_in_file(markdown_path, replacements, output_path)

def validate_markdown_citations_without_highlight(markdown_path: str, bibtex_path: str):
    """向后兼容：验证Markdown引用（不高亮）"""
    all_valid, missing_keys, suggestions = _validator.validate_citations(markdown_path, bibtex_path)
    return all_valid, missing_keys, suggestions

# markdown_tools.py 兼容性
def adjust_heading_levels(content: str, adjustment: int = 1):
    """向后兼容：调整标题级别"""
    return _markdown.adjust_heading_levels(content, adjustment)

def extract_heading_structure(content: str):
    """向后兼容：提取标题结构"""
    return _markdown.extract_heading_structure(content)

def apply_custom_heading_levels(content: str, level_mapping: Dict[str, int]):
    """向后兼容：应用自定义标题级别"""
    return _markdown.apply_custom_heading_levels(content, level_mapping)

def convert_lists_to_headings(content: str, max_depth: int = 3, start_level: int = 2):
    """向后兼容：转换列表为标题"""
    return _markdown.convert_lists_to_headings(content, max_depth, start_level)

# bib_merger.py 兼容性
def load_bibtex(file_path: str):
    """向后兼容：加载BibTeX文件"""
    return _bibtex.load_bibtex_entries(file_path)

def replace_citations_with_links(markdown_content: str, bibtex_entries: Dict[str, Dict]):
    """向后兼容：替换引用为链接"""
    return _bibtex.replace_citations_with_links(markdown_content, bibtex_entries)

def merge_markdown_with_bibtex(
    markdown_path: str,
    bibtex_path: str,
    output_path: str,
    url_deduplicate: bool = True,
    link_text_pattern: str = "title"
):
    """向后兼容：合并Markdown和BibTeX"""
    return _bibtex.merge_markdown_with_bibtex(
        markdown_path, bibtex_path, output_path, url_deduplicate, link_text_pattern
    )

# 导出所有向后兼容的函数
__all__ = [
    # loader.py
    'load_conversation',
    # detector.py
    'is_deep_research_message',
    # response_extractor.py
    'extract_deep_research_content',
    # content_processor.py
    'process_content_with_direct_references',
    'process_content_with_content_references',
    # main.py
    'process_conversation',
    # citation_validator.py
    'validate_markdown_citations',
    'highlight_missing_citations',
    'generate_missing_citation_report',
    'replace_citation',
    'replace_citations_in_file',
    'validate_markdown_citations_without_highlight',
    # markdown_tools.py
    'adjust_heading_levels',
    'extract_heading_structure',
    'apply_custom_heading_levels',
    'convert_lists_to_headings',
    # bib_merger.py
    'load_bibtex',
    'replace_citations_with_links',
    'merge_markdown_with_bibtex',
]
