"""
内容处理器模块，用于处理Markdown内容和引用标记
"""

import re
from typing import List, Tuple, Dict, Set
from ..logger import setup_logger
from ..models import Message, Citation

# 配置日志
logger = setup_logger(name=__name__)


def generate_cite_key(
    title: str, authors: list = None, year: int = None, index: int = 0
) -> str:
    """
    生成引用键

    Args:
        title: 文献标题
        authors: 作者列表
        year: 发表年份
        index: 引用索引

    Returns:
        生成的引用键
    """
    if not authors:
        authors = []

    # 尝试基于作者+年份生成键
    if authors and len(authors) > 0 and year:
        # 提取第一个作者姓氏
        first_author = (
            authors[0].split()[-1].lower() if " " in authors[0] else authors[0].lower()
        )
        # 去掉非字母字符
        first_author = "".join(c for c in first_author if c.isalpha())
        if first_author and len(first_author) > 0:
            return f"{first_author}{year}"

    # 如果无法从作者+年份生成，尝试从标题生成
    if title and len(title) > 0:
        # 提取标题中的关键词
        keywords = [
            word.lower()
            for word in re.findall(r"\b\w+\b", title)
            if len(word) > 3
            and word.lower()
            not in ["with", "using", "from", "based", "that", "this", "and", "the"]
        ]
        if keywords and len(keywords) > 0:
            keyword = keywords[0]
            if year:
                return f"{keyword}{year}"
            else:
                return f"{keyword}"

    # 如果以上方法都失败，使用默认格式
    return f"cite{index + 1}"


def process_content_with_direct_references(
    content: str, message: Message, mode: str = "citations"
) -> Tuple[str, List[Citation]]:
    """
    处理内容，直接使用消息中的citations或content_references信息替换原文中的引用

    Args:
        content: 原始内容文本
        message: 包含引用信息的消息对象
        mode: 引用处理模式，可选值为"citations"或"content_references"，默认为"citations"

    Returns:
        处理后的内容和引用列表
    """
    if not message.metadata:
        return content, []

    # 收集所有引用信息
    citations_info = []  # [(start_ix, end_ix, citation对象)]

    # 已使用的引用键集合
    used_keys: Set[str] = set()

    # 处理content_references字段
    if mode == "content_references" and "content_references" in message.metadata:
        for i, ref in enumerate(message.metadata.get("content_references", [])):
            try:
                ref_error = False
                # 检查是否为错误条目
                if ref.get("invalid", False):
                    logger.warning(f"content_reference {i} 被标记为无效条目，跳过处理")
                    ref_error = True

                if "start_idx" in ref and "end_idx" in ref:
                    start_ix = ref["start_idx"]
                    end_ix = ref["end_idx"]
                elif "start_ix" in ref and "end_ix" in ref:
                    start_ix = ref["start_ix"]
                    end_ix = ref["end_ix"]
                else:
                    logger.warning(f"content_reference {i} 中缺少位置信息")
                    continue

                # 检查位置有效性
                if not (0 <= start_ix < end_ix <= len(content)):
                    logger.warning(
                        f"content_reference {i} 位置无效: {start_ix}-{end_ix}, 内容长度: {len(content)}"
                    )
                    continue

                # 提取元数据
                title = ref.get("title", "")
                if not title and "metadata" in ref:
                    title = ref.get("metadata", {}).get("title", "未知标题")

                url = ref.get("url", "")
                text = ref.get("snippet", "") or ref.get("matched_text", "")

                # 尝试从引用文本中提取作者信息
                authors = []
                author_text = ref.get("attribution", "")
                if author_text:
                    authors = [author_text]

                # 尝试提取年份
                year = None
                year_match = re.search(
                    r"\b(19|20)\d{2}\b", title + " " + (text or "") + " " + (url or "")
                )
                if year_match:
                    year = int(year_match.group(0))

                # 生成引用键
                base_key = generate_cite_key(title, authors, year, i)
                key = base_key

                # 确保键唯一
                counter = 1
                while key in used_keys:
                    key = f"{base_key}{counter}"
                    counter += 1
                used_keys.add(key)

                # 创建引用对象
                citation = Citation(
                    key=key,
                    title=title,
                    authors=authors,
                    year=year,
                    url=url,
                    text=text,
                    error=ref_error,
                )

                # 尝试从URL中提取DOI
                if url and "doi.org" in url:
                    doi_match = re.search(r"doi\.org/(.+?)($|[#?])", url)
                    if doi_match:
                        citation.doi = doi_match.group(1)

                # 尝试从URL或标题中推断期刊/出版商
                if "arxiv" in (url or "").lower() or "arxiv" in title.lower():
                    citation.journal = "arXiv"
                elif "acl" in (url or "").lower():
                    citation.journal = "ACL"
                elif "neurips" in (url or "").lower():
                    citation.journal = "NeurIPS"

                # 保存引用信息
                citations_info.append((start_ix, end_ix, citation))

            except Exception as e:
                logger.warning(f"处理content_reference {i} 时出错: {str(e)}")
                continue

    # 处理citations字段
    elif mode == "citations" and "citations" in message.metadata:
        for i, cite_data in enumerate(message.metadata.get("citations", [])):
            try:
                ref_error = False
                # 检查是否为错误条目
                if "invalid_reason" in cite_data:
                    logger.warning(
                        f"citation {i} 被标记为无效条目，原因: {cite_data['invalid_reason']}，跳过处理"
                    )
                    ref_error = True

                if "start_ix" in cite_data and "end_ix" in cite_data:
                    start_ix = cite_data["start_ix"]
                    end_ix = cite_data["end_ix"]
                else:
                    logger.warning(f"citation {i} 中缺少位置信息")
                    continue

                # 检查位置有效性
                if not (0 <= start_ix < end_ix <= len(content)):
                    logger.warning(
                        f"citation {i} 位置无效: {start_ix}-{end_ix}, 内容长度: {len(content)}"
                    )
                    continue

                # 提取元数据
                metadata = cite_data.get("metadata", {})
                title = metadata.get("title", "未知标题")

                # 尝试从URL或标题中提取作者信息
                authors = []
                text = metadata.get("text", "")

                # 尝试提取年份
                year = None
                if "pub_date" in metadata and metadata["pub_date"]:
                    # 尝试从pub_date中提取年份
                    year_match = re.search(r"\b(19|20)\d{2}\b", metadata["pub_date"])
                    if year_match:
                        year = int(year_match.group(0))
                else:
                    # 尝试从标题或URL中提取年份
                    year_match = re.search(
                        r"\b(19|20)\d{2}\b", title + " " + (metadata.get("url") or "")
                    )
                    if year_match:
                        year = int(year_match.group(0))

                # 生成引用键
                base_key = generate_cite_key(title, authors, year, i)
                key = base_key

                # 确保键唯一
                counter = 1
                while key in used_keys:
                    key = f"{base_key}{counter}"
                    counter += 1
                used_keys.add(key)

                # 创建引用对象
                citation = Citation(
                    key=key,
                    title=title,
                    authors=authors,
                    year=year,
                    url=metadata.get("url"),
                    text=text,
                    error=ref_error,
                )

                # 保存引用信息
                citations_info.append((start_ix, end_ix, citation))

            except Exception as e:
                logger.warning(f"处理citation {i} 时出错: {str(e)}")
                continue

    # 如果没有收集到有效引用，直接返回原内容
    if not citations_info:
        return content, []

    # 构建引用替换计划
    # 首先按照开始位置排序
    citations_info.sort(key=lambda x: x[0])

    # 检测重叠冲突
    for i in range(1, len(citations_info)):
        prev_end = citations_info[i - 1][1]
        curr_start = citations_info[i][0]
        if curr_start < prev_end:
            logger.warning(
                f"检测到引用重叠: {citations_info[i - 1][0]}-{prev_end} 与 {curr_start}-{citations_info[i][1]}"
            )
            # 简单处理：保留前一个引用，调整当前引用的开始位置
            citations_info[i] = (prev_end, citations_info[i][1], citations_info[i][2])

    # 实际执行替换
    # 使用新字符串来构建结果，避免在原始字符串上修改导致的位置偏移
    result_parts = []
    last_end = 0

    for start_ix, end_ix, citation in citations_info:
        # 添加引用之前的文本
        if start_ix > last_end:
            result_parts.append(content[last_end:start_ix])

        # 添加引用标记
        if not citation.error:
            result_parts.append(f" [@{citation.key}] ")

        last_end = end_ix

    # 添加最后一个引用后的文本
    if last_end < len(content):
        result_parts.append(content[last_end:])

    modified_content = "".join(result_parts)

    return modified_content, [citation for _, _, citation in citations_info]
