"""
BibTeX处理工具
"""

import re
from typing import Dict, Any, List, Optional, Tuple, Set
from pathlib import Path
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


class BibtexProcessor:
    """BibTeX处理器，提供各种BibTeX处理功能"""

    def __init__(self):
        """初始化处理器"""
        pass

    def load_bibtex_entries(self, bib_path: str) -> Dict[str, Dict]:
        """
        加载BibTeX文件并解析为字典

        Args:
            bib_path: BibTeX文件路径

        Returns:
            包含引用信息的字典，键为citekey
        """
        try:
            # 直接读取BibTeX文件
            with open(bib_path, "r", encoding="utf-8") as f:
                content = f.read()

            entries = {}

            # 使用状态机方法解析BibTeX，以处理URL中可能包含@符号的情况
            lines = content.splitlines()
            current_entry = None
            current_key = None
            current_type = None
            current_content = []
            in_entry = False
            brace_count = 0

            for line in lines:
                line_stripped = line.strip()

                # 检测条目开始
                if not in_entry and line_stripped.startswith("@"):
                    # 匹配条目类型和键
                    entry_start = re.match(r"@(\w+)\s*{\s*([^,]*)", line_stripped)
                    if entry_start:
                        in_entry = True
                        current_type = entry_start.group(1).lower()
                        current_key = entry_start.group(2).strip()
                        current_content = []
                        brace_count = line_stripped.count("{") - line_stripped.count("}")
                        continue

                if in_entry:
                    current_content.append(line)
                    brace_count += line.count("{") - line.count("}")

                    # 检测条目结束
                    if brace_count <= 0:
                        # 解析当前条目
                        entry_text = "\n".join(current_content)
                        entry_data = self._parse_bibtex_entry(entry_text, current_type)
                        if current_key and entry_data:
                            entries[current_key] = entry_data

                        # 重置状态
                        in_entry = False
                        current_entry = None
                        current_key = None
                        current_type = None
                        current_content = []
                        brace_count = 0

            logger.info(f"成功加载 {len(entries)} 个BibTeX条目")
            return entries

        except Exception as e:
            logger.error(f"加载BibTeX文件失败: {str(e)}")
            return {}

    def _parse_bibtex_entry(self, entry_text: str, entry_type: str) -> Dict[str, str]:
        """
        解析单个BibTeX条目

        Args:
            entry_text: 条目文本
            entry_type: 条目类型

        Returns:
            解析后的条目字典
        """
        entry = {"type": entry_type}

        # 匹配字段的正则表达式
        field_pattern = r"(\w+)\s*=\s*[{\"](.*?)[}\"](?=\s*,|\s*$)"

        # 处理多行字段值
        cleaned_text = re.sub(r"\n\s*", " ", entry_text)

        for match in re.finditer(field_pattern, cleaned_text, re.DOTALL):
            field_name = match.group(1).lower()
            field_value = match.group(2).strip()

            # 清理字段值
            field_value = re.sub(r"\s+", " ", field_value)
            entry[field_name] = field_value

        return entry

    def load_bibtex(self, file_path: str) -> Dict[str, Dict[str, Any]]:
        """
        加载BibTeX文件（向后兼容方法）

        Args:
            file_path: BibTeX文件路径

        Returns:
            BibTeX条目字典
        """
        return self.load_bibtex_entries(file_path)

    def format_entry(self, entry: Dict[str, Any]) -> str:
        """
        格式化BibTeX条目
        
        Args:
            entry: BibTeX条目
            
        Returns:
            格式化后的BibTeX字符串
        """
        return format_bibtex_entry(entry)
    
    def validate_entry(self, entry: Dict[str, Any]) -> bool:
        """
        验证BibTeX条目
        
        Args:
            entry: BibTeX条目
            
        Returns:
            是否有效
        """
        return validate_bibtex_entry(entry)
    
    def normalize_key(self, key: str) -> str:
        """
        规范化BibTeX键
        
        Args:
            key: 原始键
            
        Returns:
            规范化后的键
        """
        return normalize_bibtex_key(key)
    
    def merge_entries(
        self, 
        entries1: Dict[str, Dict[str, Any]], 
        entries2: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Dict[str, Any]]:
        """
        合并两个BibTeX条目字典
        
        Args:
            entries1: 第一个条目字典
            entries2: 第二个条目字典
            
        Returns:
            合并后的条目字典
        """
        merged = entries1.copy()
        merged.update(entries2)
        return merged
    
    def filter_entries(
        self, 
        entries: Dict[str, Dict[str, Any]], 
        entry_type: str = None,
        has_url: bool = None,
        has_doi: bool = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        过滤BibTeX条目
        
        Args:
            entries: 条目字典
            entry_type: 条目类型过滤
            has_url: 是否有URL
            has_doi: 是否有DOI
            
        Returns:
            过滤后的条目字典
        """
        filtered = {}
        
        for key, entry in entries.items():
            # 类型过滤
            if entry_type and entry.get("type", "").lower() != entry_type.lower():
                continue
            
            # URL过滤
            if has_url is not None:
                if has_url and not entry.get("url"):
                    continue
                if not has_url and entry.get("url"):
                    continue
            
            # DOI过滤
            if has_doi is not None:
                if has_doi and not entry.get("doi"):
                    continue
                if not has_doi and entry.get("doi"):
                    continue
            
            filtered[key] = entry
        
        return filtered

    def replace_citations_with_links(
        self,
        markdown_content: str,
        bibtex_entries: Dict[str, Dict],
        url_deduplicate: bool = True,
        link_text_pattern: str = "title"
    ) -> str:
        """
        将Markdown中的引用标记替换为链接

        Args:
            markdown_content: Markdown内容
            bibtex_entries: BibTeX条目字典
            url_deduplicate: 是否去重URL
            link_text_pattern: 链接文本模式

        Returns:
            替换后的Markdown内容
        """
        # 查找所有引用标记 [@citekey]
        cite_pattern = r"\[@([^\]]+)\]"
        used_urls = set() if url_deduplicate else None

        def replace_citation(match):
            cite_key = match.group(1)
            if cite_key in bibtex_entries:
                entry = bibtex_entries[cite_key]

                # 根据模式确定链接文本
                if link_text_pattern == "title":
                    link_text = entry.get("title", cite_key)
                elif link_text_pattern == "author_year":
                    author = entry.get("author", "").split(",")[0] if entry.get("author") else "Unknown"
                    year = entry.get("year", "")
                    link_text = f"{author} ({year})" if year else author
                else:
                    link_text = cite_key

                url = entry.get("url", "")

                # URL去重处理
                if url and url_deduplicate and url in used_urls:
                    return f"[{link_text}]"

                if url:
                    if used_urls is not None:
                        used_urls.add(url)
                    return f"[{link_text}]({url})"
                else:
                    return f"[{link_text}]"
            else:
                # 保持原样
                return match.group(0)

        return re.sub(cite_pattern, replace_citation, markdown_content)

    def merge_markdown_with_bibtex(
        self,
        markdown_path: str,
        bibtex_path: str,
        output_path: str,
        url_deduplicate: bool = True,
        link_text_pattern: str = "title"
    ) -> str:
        """
        将Markdown与BibTeX合并

        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径
            output_path: 输出文件路径
            url_deduplicate: 是否去重URL
            link_text_pattern: 链接文本模式

        Returns:
            输出文件路径
        """
        try:
            # 加载Markdown内容
            with open(markdown_path, "r", encoding="utf-8") as f:
                markdown_content = f.read()

            # 加载BibTeX条目
            bibtex_entries = self.load_bibtex_entries(bibtex_path)

            # 替换引用
            processed_content = self.replace_citations_with_links(
                markdown_content, bibtex_entries, url_deduplicate, link_text_pattern
            )

            # 保存结果
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(processed_content)

            logger.info(f"合并完成，输出文件: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"合并Markdown和BibTeX时发生错误: {str(e)}")
            raise
