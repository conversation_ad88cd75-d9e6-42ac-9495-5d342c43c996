"""
Markdown处理工具
"""

import re
from typing import List, Dict, Any, Tuple, Union, Optional, Set
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


class MarkdownProcessor:
    """Markdown处理器，提供各种Markdown处理功能"""

    def __init__(self):
        """初始化处理器"""
        pass

    def adjust_heading_levels(
        self,
        markdown_content: str,
        adjustment: int = 0,
        first_level_as_title: bool = False,
        min_level: int = 1,
        max_level: int = 6,
    ) -> str:
        """
        调整Markdown文档中所有标题的层级

        Args:
            markdown_content: 要处理的Markdown内容
            adjustment: 标题级别的调整量，正数增加层级，负数减少层级
            first_level_as_title: 是否将第一个一级标题保留为文章标题
            min_level: 允许的最小标题级别 (默认为1)
            max_level: 允许的最大标题级别 (默认为6)

        Returns:
            处理后的Markdown内容
        """
        # 正则表达式匹配标题行
        heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

        # 如果需要将第一个一级标题保留为文章标题
        first_h1 = None
        if first_level_as_title:
            h1_match = re.search(r"^#\s+(.*?)(?:\s+#+)?$", markdown_content, re.MULTILINE)
            if h1_match:
                first_h1 = h1_match.group(0)
                first_h1_pos = h1_match.start()
                end_pos = h1_match.end()
                # 临时替换第一个一级标题，以便后续处理
                markdown_content = (
                    markdown_content[:first_h1_pos]
                    + "%%FIRST_TITLE_PLACEHOLDER%%"
                    + markdown_content[end_pos:]
                )

        # 处理所有标题
        def adjust_heading(match):
            heading = match.group(1)
            content = match.group(2)
            level = len(heading)
            new_level = max(min(level + adjustment, max_level), min_level)
            return "#" * new_level + " " + content

        processed_content = heading_pattern.sub(adjust_heading, markdown_content)

        # 恢复第一个一级标题（如果需要）
        if first_level_as_title and first_h1:
            processed_content = processed_content.replace(
                "%%FIRST_TITLE_PLACEHOLDER%%", first_h1
            )

        return processed_content

    def extract_heading_structure(
        self, markdown_content: str
    ) -> List[Dict[str, Union[int, str]]]:
        """
        提取Markdown文档中的标题结构

        Args:
            markdown_content: Markdown内容

        Returns:
            标题结构列表，每个标题包含级别和内容
        """
        heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

        headings = []
        for match in heading_pattern.finditer(markdown_content):
            level = len(match.group(1))
            content = match.group(2).strip()
            headings.append({"level": level, "content": content})

        return headings

    def apply_custom_heading_levels(
        self, markdown_content: str, level_mapping: Dict[str, int]
    ) -> str:
        """
        根据自定义映射调整标题级别

        Args:
            markdown_content: Markdown内容
            level_mapping: 标题内容到目标级别的映射

        Returns:
            调整后的Markdown内容
        """
        heading_pattern = re.compile(r"^(#{1,6})\s+(.*?)(?:\s+#+)?$", re.MULTILINE)

        def adjust_heading(match):
            heading = match.group(1)
            content = match.group(2).strip()

            # 检查是否有自定义级别
            if content in level_mapping:
                new_level = level_mapping[content]
                return "#" * new_level + " " + content
            else:
                # 保持原级别
                return match.group(0)

        return heading_pattern.sub(adjust_heading, markdown_content)

    def convert_lists_to_headings(
        self,
        markdown_content: str,
        max_depth: int = 3,
        start_level: int = 2,
        patterns: Optional[List[str]] = None,
    ) -> str:
        """
        将特定的列表项转换为标题

        Args:
            markdown_content: Markdown内容
            max_depth: 最大转换深度
            start_level: 起始标题级别
            patterns: 要转换的列表项模式列表

        Returns:
            转换后的Markdown内容
        """
        if patterns is None:
            patterns = [
                r"^\s*[-*+]\s+(.+)$",  # 无序列表
                r"^\s*\d+\.\s+(.+)$",  # 有序列表
            ]

        lines = markdown_content.split("\n")
        result_lines = []

        for line in lines:
            converted = False

            # 检查每个模式
            for depth, pattern in enumerate(patterns[:max_depth]):
                match = re.match(pattern, line)
                if match:
                    content = match.group(1).strip()
                    level = start_level + depth
                    result_lines.append("#" * level + " " + content)
                    converted = True
                    break

            if not converted:
                result_lines.append(line)

        return "\n".join(result_lines)

    def replace_citations_with_links(
        self, markdown_content: str, bibtex_entries: Dict[str, Dict]
    ) -> str:
        """
        将Markdown中的引用标记替换为链接

        Args:
            markdown_content: Markdown内容
            bibtex_entries: BibTeX条目字典

        Returns:
            替换后的Markdown内容
        """
        # 查找所有引用标记 [@citekey]
        cite_pattern = r"\[@([^\]]+)\]"

        def replace_citation(match):
            cite_key = match.group(1)
            if cite_key in bibtex_entries:
                entry = bibtex_entries[cite_key]
                title = entry.get("title", cite_key)
                url = entry.get("url", "")

                if url:
                    return f"[{title}]({url})"
                else:
                    return f"[{title}]"
            else:
                # 保持原样
                return match.group(0)

        return re.sub(cite_pattern, replace_citation, markdown_content)

    def merge_with_bibtex(
        self,
        markdown_path: str,
        bibtex_path: str,
        output_path: str,
        url_deduplicate: bool = True,
        link_text_pattern: str = "title"
    ) -> str:
        """
        将Markdown与BibTeX合并

        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径
            output_path: 输出文件路径
            url_deduplicate: 是否去重URL
            link_text_pattern: 链接文本模式

        Returns:
            输出文件路径
        """
        try:
            # 加载Markdown内容
            with open(markdown_path, "r", encoding="utf-8") as f:
                markdown_content = f.read()

            # 加载BibTeX条目
            from .bibtex import BibtexProcessor
            bibtex_processor = BibtexProcessor()
            bibtex_entries = bibtex_processor.load_bibtex_entries(bibtex_path)

            # 替换引用
            processed_content = self.replace_citations_with_links(markdown_content, bibtex_entries)

            # 保存结果
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(processed_content)

            logger.info(f"合并完成，输出文件: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"合并Markdown和BibTeX时发生错误: {str(e)}")
            raise
    
    def merge_with_bibtex(
        self,
        markdown_path: str,
        bibtex_path: str,
        output_path: str,
        url_deduplicate: bool = True,
        link_text_pattern: str = "title"
    ) -> str:
        """
        将Markdown与BibTeX合并
        
        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径
            output_path: 输出文件路径
            url_deduplicate: 是否去重URL
            link_text_pattern: 链接文本模式
            
        Returns:
            输出文件路径
        """
        from ..parser.bib_merger import merge_markdown_with_bibtex
        return merge_markdown_with_bibtex(
            markdown_path, bibtex_path, output_path, url_deduplicate, link_text_pattern
        )
