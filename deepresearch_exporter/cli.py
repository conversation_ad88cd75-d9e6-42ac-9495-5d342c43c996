"""
命令行接口，用于处理用户输入和执行导出操作
"""

import os
import sys
import click
from pathlib import Path

from .core.processor import ConversationProcessor
from .tools.markdown import MarkdownProcessor
from .tools.bibtex import BibtexProcessor
from .exporter import export_response, export_response_with_docx, export_docx
from .logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


@click.group()
def cli():
    """Deep Research导出工具命令行接口"""
    pass


@cli.command()
@click.argument(
    "input_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.option(
    "--output-dir",
    "-o",
    type=click.Path(file_okay=False, dir_okay=True),
    default=None,
    help='输出目录路径，默认从配置读取',
)
@click.option(
    "--name",
    "-n",
    type=str,
    help="输出文件的基本名称（不含扩展名），默认使用输入文件名",
)
@click.option(
    "--mode",
    "-m",
    type=click.Choice(["citations", "content_references"]),
    default=None,
    help='引用处理模式，可选值为"citations"或"content_references"，默认从配置读取',
)
@click.option("--docx", "-d", is_flag=True, help="同时导出为DOCX格式")
@click.option(
    "--csl",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
    help="CSL引用样式文件路径",
)
@click.option(
    "--reference-doc",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
    help="DOCX参考文档路径",
)
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志信息")
def export(input_file, output_dir, name, mode, docx, csl, reference_doc, verbose):
    """
    处理ChatGPT导出的Deep Research对话，并导出为Markdown和BibTeX格式。

    INPUT_FILE: ChatGPT导出的JSON文件路径
    """
    # 加载配置
    from .config import get_config
    config = get_config()

    # 使用配置的默认值
    if output_dir is None:
        output_dir = config.export.output_dir
    if mode is None:
        mode = config.export.default_mode
    if csl is None:
        csl = config.export.default_csl_path
    if reference_doc is None:
        reference_doc = config.export.default_reference_doc

    # 配置日志级别
    import logging
    log_level = logging.DEBUG if verbose else logging.INFO
    # 重新配置全局日志级别
    root_logger = setup_logger(level=log_level, name=None)

    try:
        # 确保路径是绝对路径
        input_path = Path(input_file).resolve()
        output_path = Path(output_dir).resolve()

        logger.info(f"正在处理文件: {input_path}")
        logger.info(f"使用引用处理模式: {mode}")

        # 如果未指定输出文件名，使用输入文件名（不含扩展名）
        if not name:
            name = input_path.stem

        # 确保输出目录存在
        output_path.mkdir(parents=True, exist_ok=True)

        # 处理对话
        logger.info(f"开始解析对话文件: {input_path}")
        processor = ConversationProcessor()
        response = processor.process_conversation(str(input_path), mode=mode)
        logger.info(f"成功解析对话，提取了 {len(response.citations)} 条引用")

        # 导出结果
        logger.info(f"开始导出到目录: {output_path}")

        if docx:
            md_path, bib_path, docx_path = export_response_with_docx(
                response,
                str(output_path),
                name,
                csl_path=csl,
                reference_doc=reference_doc,
            )
            logger.info(f"成功导出Markdown文件: {md_path}")
            logger.info(f"成功导出BibTeX文件: {bib_path}")
            logger.info(f"成功导出DOCX文件: {docx_path}")
        else:
            md_path, bib_path = export_response(response, str(output_path), name)
            logger.info(f"成功导出Markdown文件: {md_path}")
            logger.info(f"成功导出BibTeX文件: {bib_path}")

        logger.info(f"共处理了 {len(response.citations)} 条引用")

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {str(e)}", err=True)
        logger.error(f"文件未找到: {str(e)}")
        sys.exit(1)
    except ValueError as e:
        logger.error(f"处理失败: {str(e)}", err=True)
        logger.error(f"值错误: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", err=True)
        logger.error(f"未预期的错误: {str(e)}", exc_info=True)
        sys.exit(1)


@cli.command()
@click.argument(
    "markdown_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.argument(
    "bibtex_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.option(
    "--output",
    "-o",
    type=click.Path(file_okay=True, dir_okay=False),
    required=True,
    help="输出DOCX文件路径",
)
@click.option(
    "--csl",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
    help="CSL引用样式文件路径",
)
@click.option(
    "--reference-doc",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
    help="DOCX参考文档路径",
)
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志信息")
def to_docx(markdown_file, bibtex_file, output, csl, reference_doc, verbose):
    """
    将已有的Markdown和BibTeX文件转换为DOCX格式。

    MARKDOWN_FILE: Markdown文件路径

    BIBTEX_FILE: BibTeX文件路径
    """
    # 配置日志级别
    import logging

    log_level = logging.DEBUG if verbose else logging.INFO
    # 重新配置全局日志级别
    root_logger = setup_logger(level=log_level, name=None)

    try:
        # 确保路径是绝对路径
        markdown_path = Path(markdown_file).resolve()
        bibtex_path = Path(bibtex_file).resolve()
        output_path = Path(output).resolve()

        logger.info(f"正在处理Markdown文件: {markdown_path}")
        logger.info(f"使用BibTeX文件: {bibtex_path}")

        # 导出DOCX
        export_docx(
            str(markdown_path),
            str(bibtex_path),
            str(output_path),
            csl_path=csl,
            reference_doc=reference_doc,
        )

        logger.info(f"成功导出DOCX文件: {output_path}")

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        sys.exit(1)


@cli.command()
@click.option("--share", is_flag=True, help="创建可公开访问的链接")
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志信息")
@click.option(
    "--kwargs",
    "-k",
    multiple=True,
    type=str,
    help="传递给launch_ui的参数，格式为key=value",
)
def web(share, verbose, kwargs):
    """启动Web界面，提供图形化操作"""
    # 配置日志级别
    import logging

    log_level = logging.DEBUG if verbose else logging.INFO
    # 重新配置全局日志级别
    root_logger = setup_logger(level=log_level, name=None)

    try:
        from .ui import launch_ui

        logger.info(f"正在启动Web界面，共享模式: {share}")

        # 处理额外的关键字参数
        extra_kwargs = {}
        for kv in kwargs:
            if "=" in kv:
                key, value = kv.split("=", 1)
                extra_kwargs[key] = value

        launch_ui(share=share, **extra_kwargs)
    except ImportError as e:
        logger.error(f"启动Web界面失败: {str(e)}")
        logger.error("请确保已安装gradio库: pip install gradio>=5.32.0")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动Web界面失败: {str(e)}", exc_info=True)
        sys.exit(1)


@cli.command(name="convert-lists")
@click.argument(
    "input_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.option(
    "--output",
    "-o",
    type=click.Path(file_okay=True, dir_okay=False),
    help="输出Markdown文件路径，默认为在原文件名后添加_converted后缀",
)
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志信息")
def convert_lists(input_file, output, verbose):
    """
    将Markdown文档中的无序列表转换为多级标题格式。

    处理带有加粗小标题的无序列表项，将其转换为多级标题。
    例如，将"- **标题:** 内容"转换为"### 标题\n内容"。

    INPUT_FILE: 输入的Markdown文件路径
    """
    # 配置日志级别
    import logging

    log_level = logging.DEBUG if verbose else logging.INFO
    # 重新配置全局日志级别
    root_logger = setup_logger(level=log_level, name=None)

    try:
        # 确保路径是绝对路径
        input_path = Path(input_file).resolve()

        logger.info(f"正在处理文件: {input_path}")

        # 如果未指定输出文件路径，使用默认路径
        if not output:
            output_path = input_path.with_name(
                f"{input_path.stem}_converted{input_path.suffix}"
            )
        else:
            output_path = Path(output).resolve()

        # 读取输入文件
        logger.info(f"读取Markdown文件: {input_path}")
        with open(input_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 处理内容
        logger.info("开始转换无序列表为多级标题")
        markdown_processor = MarkdownProcessor()
        processed_content = markdown_processor.convert_lists_to_headings(content)

        # 写入输出文件
        logger.info(f"写入转换后的内容到: {output_path}")
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(processed_content)

        logger.info(f"转换完成，输出文件: {output_path}")

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        sys.exit(1)


@cli.command(name="merge-citations")
@click.argument(
    "markdown_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.argument(
    "bibtex_file",
    type=click.Path(exists=True, file_okay=True, dir_okay=False, readable=True),
)
@click.option(
    "--output",
    "-o",
    type=click.Path(file_okay=True, dir_okay=False),
    required=True,
    help="输出Markdown文件路径",
)
@click.option("--deduplicate-urls", "-d", is_flag=True, help="对相似URL进行去重处理")
@click.option(
    "--link-text",
    "-l",
    type=str,
    default="[{index}]",
    help='链接文本模式，可使用{index}作为序号占位符，默认为"[{index}]"',
)
@click.option("--verbose", "-v", is_flag=True, help="显示详细日志信息")
def merge_citations(
    markdown_file, bibtex_file, output, deduplicate_urls, link_text, verbose
):
    """
    将Markdown文件中的引用标记替换为BibTeX中的URL链接。

    查找Markdown中的[@citekey]格式引用，将其替换为指向BibTeX中对应URL的链接。

    MARKDOWN_FILE: Markdown文件路径

    BIBTEX_FILE: BibTeX文件路径
    """
    # 配置日志级别
    import logging

    log_level = logging.DEBUG if verbose else logging.INFO
    # 重新配置全局日志级别
    root_logger = setup_logger(level=log_level, name=None)

    try:
        # 确保路径是绝对路径
        markdown_path = Path(markdown_file).resolve()
        bibtex_path = Path(bibtex_file).resolve()
        output_path = Path(output).resolve()

        logger.info(f"正在处理Markdown文件: {markdown_path}")
        logger.info(f"使用BibTeX文件: {bibtex_path}")
        logger.info(f"URL去重处理: {'开启' if deduplicate_urls else '关闭'}")
        logger.info(f"链接文本模式: {link_text}")

        # 合并处理
        bibtex_processor = BibtexProcessor()
        result_path = bibtex_processor.merge_markdown_with_bibtex(
            str(markdown_path),
            str(bibtex_path),
            str(output_path),
            url_deduplicate=deduplicate_urls,
            link_text_pattern=link_text,
        )

        logger.info(f"成功导出合并后的Markdown文件: {result_path}")

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        sys.exit(1)


# 兼容旧版本的命令行接口
def main():
    """旧版本命令行接口的入口点，保持向后兼容"""
    # 检查是否有子命令
    if len(sys.argv) > 1 and sys.argv[1] in [
        "export",
        "web",
        "to_docx",
        "convert-lists",
        "merge-citations",
    ]:
        cli()
    else:
        # 如果没有子命令，默认使用export命令
        # 将命令行参数传递给export命令
        sys.argv.insert(1, "export")
        cli()


if __name__ == "__main__":
    main()
