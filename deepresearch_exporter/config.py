"""
配置管理模块，提供统一的配置加载和管理功能
"""

import os
from typing import Optional, Dict, Any, Union
from pathlib import Path
from dataclasses import dataclass, field
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


@dataclass
class LLMTranslationConfig:
    """LLM翻译配置"""
    base_url: str = "http://localhost:11434/v1"
    api_key: str = "ollama"
    model: str = "qwen2.5:32b"
    source_language: str = "English"
    target_language: str = "Chinese"
    domain: Optional[str] = None
    temperature: float = 0.3
    top_p: float = 0.9
    batch_size: int = 5
    max_tokens: int = 4000
    max_requests: int = 5

    @classmethod
    def from_env(cls) -> "LLMTranslationConfig":
        """从环境变量创建配置"""
        return cls(
            base_url=os.getenv("LLM_TRANSLATION_BASE_URL", cls.base_url),
            api_key=os.getenv("LLM_TRANSLATION_API_KEY", cls.api_key),
            model=os.getenv("LLM_TRANSLATION_MODEL", cls.model),
            source_language=os.getenv("LLM_TRANSLATION_SOURCE_LANGUAGE", cls.source_language),
            target_language=os.getenv("LLM_TRANSLATION_TARGET_LANGUAGE", cls.target_language),
            domain=os.getenv("LLM_TRANSLATION_DOMAIN"),
            temperature=float(os.getenv("LLM_TRANSLATION_TEMPERATURE", cls.temperature)),
            top_p=float(os.getenv("LLM_TRANSLATION_TOP_P", cls.top_p)),
            batch_size=int(os.getenv("LLM_TRANSLATION_BATCH_SIZE", cls.batch_size)),
            max_tokens=int(os.getenv("LLM_TRANSLATION_MAX_TOKENS", cls.max_tokens)),
            max_requests=int(os.getenv("LLM_TRANSLATION_MAX_REQUESTS", cls.max_requests)),
        )


@dataclass
class OnlineTranslationConfig:
    """在线翻译配置"""
    translator_type: str = "google"  # "google" or "deepl"
    api_key: str = ""
    source_language: Optional[str] = None
    target_language: str = "zh-CN"
    formality: Optional[str] = None  # DeepL特有: "default", "more", "less"
    preserve_formatting: bool = False
    split_sentences: str = "nonewlines"  # "nonewlines", "default", "no"
    tag_handling: Optional[str] = None  # "xml", "html"
    model_type: Optional[str] = None  # DeepL模型类型

    @classmethod
    def from_env(cls) -> "OnlineTranslationConfig":
        """从环境变量创建配置"""
        return cls(
            translator_type=os.getenv("ONLINE_TRANSLATOR_TYPE", cls.translator_type),
            api_key=os.getenv("DEEPL_API_KEY", cls.api_key),
            source_language=os.getenv("ONLINE_TRANSLATION_SOURCE_LANGUAGE"),
            target_language=os.getenv("ONLINE_TRANSLATION_TARGET_LANGUAGE", cls.target_language),
            formality=os.getenv("ONLINE_TRANSLATION_FORMALITY"),
            preserve_formatting=os.getenv("ONLINE_TRANSLATION_PRESERVE_FORMATTING", "false").lower() == "true",
            split_sentences=os.getenv("ONLINE_TRANSLATION_SPLIT_SENTENCES", cls.split_sentences),
            tag_handling=os.getenv("ONLINE_TRANSLATION_TAG_HANDLING"),
            model_type=os.getenv("ONLINE_TRANSLATION_MODEL_TYPE"),
        )


@dataclass
class ExportConfig:
    """导出配置"""
    output_dir: str = "output"
    default_mode: str = "citations"  # "citations" or "content_references"
    default_csl_path: Optional[str] = None
    default_reference_doc: Optional[str] = None
    auto_create_docx: bool = False

    @classmethod
    def from_env(cls) -> "ExportConfig":
        """从环境变量创建配置"""
        return cls(
            output_dir=os.getenv("EXPORT_OUTPUT_DIR", cls.output_dir),
            default_mode=os.getenv("EXPORT_DEFAULT_MODE", cls.default_mode),
            default_csl_path=os.getenv("EXPORT_DEFAULT_CSL_PATH"),
            default_reference_doc=os.getenv("EXPORT_DEFAULT_REFERENCE_DOC"),
            auto_create_docx=os.getenv("EXPORT_AUTO_CREATE_DOCX", "false").lower() == "true",
        )


@dataclass
class UIConfig:
    """UI配置"""
    title: str = "Deep Research 导出工具"
    theme: Optional[str] = None
    share: bool = False
    server_name: str = "127.0.0.1"
    server_port: Optional[int] = None
    auth: Optional[tuple] = None
    max_file_size: int = 100 * 1024 * 1024  # 100MB

    @classmethod
    def from_env(cls) -> "UIConfig":
        """从环境变量创建配置"""
        auth = None
        username = os.getenv("UI_AUTH_USERNAME")
        password = os.getenv("UI_AUTH_PASSWORD")
        if username and password:
            auth = (username, password)

        return cls(
            title=os.getenv("UI_TITLE", cls.title),
            theme=os.getenv("UI_THEME"),
            share=os.getenv("UI_SHARE", "false").lower() == "true",
            server_name=os.getenv("UI_SERVER_NAME", cls.server_name),
            server_port=int(os.getenv("UI_SERVER_PORT")) if os.getenv("UI_SERVER_PORT") else None,
            auth=auth,
            max_file_size=int(os.getenv("UI_MAX_FILE_SIZE", cls.max_file_size)),
        )


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    show_time: bool = True
    show_path: bool = False
    log_file: Optional[str] = None

    @classmethod
    def from_env(cls) -> "LoggingConfig":
        """从环境变量创建配置"""
        return cls(
            level=os.getenv("LOG_LEVEL", cls.level),
            show_time=os.getenv("LOG_SHOW_TIME", "true").lower() == "true",
            show_path=os.getenv("LOG_SHOW_PATH", "false").lower() == "true",
            log_file=os.getenv("LOG_FILE"),
        )


@dataclass
class AppConfig:
    """应用程序总配置"""
    llm_translation: LLMTranslationConfig = field(default_factory=LLMTranslationConfig)
    online_translation: OnlineTranslationConfig = field(default_factory=OnlineTranslationConfig)
    export: ExportConfig = field(default_factory=ExportConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

    @classmethod
    def from_env(cls) -> "AppConfig":
        """从环境变量创建完整配置"""
        return cls(
            llm_translation=LLMTranslationConfig.from_env(),
            online_translation=OnlineTranslationConfig.from_env(),
            export=ExportConfig.from_env(),
            ui=UIConfig.from_env(),
            logging=LoggingConfig.from_env(),
        )

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "llm_translation": self.llm_translation.__dict__,
            "online_translation": self.online_translation.__dict__,
            "export": self.export.__dict__,
            "ui": self.ui.__dict__,
            "logging": self.logging.__dict__,
        }


# 全局配置实例
_config: Optional[AppConfig] = None


def get_config() -> AppConfig:
    """获取全局配置实例"""
    global _config
    if _config is None:
        _config = AppConfig.from_env()
    return _config


def reload_config() -> AppConfig:
    """重新加载配置"""
    global _config
    load_dotenv(override=True)  # 重新加载环境变量
    _config = AppConfig.from_env()
    return _config


def update_config(**kwargs) -> None:
    """更新配置"""
    global _config
    config = get_config()
    
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)


# 便捷函数
def get_llm_translation_config() -> LLMTranslationConfig:
    """获取LLM翻译配置"""
    return get_config().llm_translation


def get_online_translation_config() -> OnlineTranslationConfig:
    """获取在线翻译配置"""
    return get_config().online_translation


def get_export_config() -> ExportConfig:
    """获取导出配置"""
    return get_config().export


def get_ui_config() -> UIConfig:
    """获取UI配置"""
    return get_config().ui


def get_logging_config() -> LoggingConfig:
    """获取日志配置"""
    return get_config().logging
