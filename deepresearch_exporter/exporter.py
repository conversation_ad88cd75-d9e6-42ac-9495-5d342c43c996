"""
导出器模块，用于将解析后的内容导出为Markdown和BibTeX格式
"""

import os
import subprocess
from typing import List, Optional, Tuple
from pathlib import Path

from .models import DeepResearchResponse, Citation
from .logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def export_markdown(response: DeepResearchResponse, output_path: str) -> str:
    """
    导出Markdown格式的文档

    Args:
        response: 处理后的Deep Research回复
        output_path: 输出文件路径

    Returns:
        输出文件的路径
    """
    # 优先使用processed_content，如果不存在则使用content
    content = (
        response.processed_content
        if response.processed_content is not None
        else response.content
    )

    # 确保输出目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    with open(output_path, "w", encoding="utf-8") as f:
        f.write(content)

    return output_path


def export_bibtex(citations: List[Citation], output_path: str) -> str:
    """
    导出BibTeX格式的引用文件

    Args:
        citations: 引用列表
        output_path: 输出文件路径

    Returns:
        输出文件的路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    with open(output_path, "w", encoding="utf-8") as f:
        for citation in citations:
            f.write(citation.to_bibtex())
            f.write("\n\n")

    return output_path


def export_response(
    response: DeepResearchResponse, output_dir: str, base_name: str
) -> tuple[str, str]:
    """
    导出处理后的回复为Markdown和BibTeX文件

    Args:
        response: 处理后的Deep Research回复
        output_dir: 输出目录
        base_name: 基本文件名（不含扩展名）

    Returns:
        (markdown文件路径, bibtex文件路径)
    """
    os.makedirs(output_dir, exist_ok=True)

    markdown_path = os.path.join(output_dir, f"{base_name}.md")
    bibtex_path = os.path.join(output_dir, f"{base_name}.bib")

    export_markdown(response, markdown_path)
    export_bibtex(response.citations, bibtex_path)

    return markdown_path, bibtex_path


def export_docx(
    markdown_path: str,
    bibtex_path: str,
    output_path: str,
    csl_path: Optional[str] = None,
    reference_doc: Optional[str] = None,
) -> str:
    """
    使用pandoc将Markdown导出为DOCX格式

    Args:
        markdown_path: Markdown文件路径
        bibtex_path: BibTeX文件路径
        output_path: 输出文件路径
        csl_path: 引用样式文件路径 (CSL)
        reference_doc: 参考文档路径 (DOCX)

    Returns:
        输出文件的路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    additional_args = ["-M", "link-citations=true", "-M", "link-bibliography=true"]
    # 构建pandoc命令
    cmd = [
        "pandoc",
        markdown_path,
        "--bibliography",
        bibtex_path,
        "-C",
        "-o",
        output_path,
    ] + additional_args

    # 默认CSL和参考文档路径
    if not csl_path:
        csl_path = str(Path(__file__).parent.parent / "templates" / "custom-ref.csl")
    if not reference_doc:
        reference_doc = str(
            Path(__file__).parent.parent / "templates" / "custom-reference.docx"
        )

    # 添加引用样式
    if csl_path:
        cmd.extend(["--csl", csl_path])

    # 添加参考文档
    if reference_doc:
        cmd.extend(["--reference-doc", reference_doc])

    try:
        # 执行pandoc命令
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("成功导出DOCX文件")
        return output_path
    except subprocess.CalledProcessError as e:
        logger.error(f"导出DOCX失败: {e.stderr}")
        raise RuntimeError(f"导出DOCX失败: {e.stderr}")
    except FileNotFoundError:
        logger.error("找不到pandoc命令，请确保已安装pandoc")
        raise RuntimeError("找不到pandoc命令，请确保已安装pandoc")


def export_response_with_docx(
    response: DeepResearchResponse,
    output_dir: str,
    base_name: str,
    csl_path: Optional[str] = None,
    reference_doc: Optional[str] = None,
) -> Tuple[str, str, str]:
    """
    导出处理后的回复为Markdown、BibTeX和DOCX文件

    Args:
        response: 处理后的Deep Research回复
        output_dir: 输出目录
        base_name: 基本文件名（不含扩展名）
        csl_path: 引用样式文件路径 (CSL)
        reference_doc: 参考文档路径 (DOCX)

    Returns:
        (markdown文件路径, bibtex文件路径, docx文件路径)
    """
    os.makedirs(output_dir, exist_ok=True)

    markdown_path = os.path.join(output_dir, f"{base_name}.md")
    bibtex_path = os.path.join(output_dir, f"{base_name}.bib")
    docx_path = os.path.join(output_dir, f"{base_name}.docx")

    export_markdown(response, markdown_path)
    export_bibtex(response.citations, bibtex_path)

    # 导出DOCX
    export_docx(markdown_path, bibtex_path, docx_path, csl_path, reference_doc)

    return markdown_path, bibtex_path, docx_path
