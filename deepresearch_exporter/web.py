"""
Web界面入口模块，提供启动Web UI的功能
"""

import gradio as gr
from deepresearch_exporter.ui.tabs import (
    create_standard_export_tab,
    create_docx_export_tab,
    create_convert_to_docx_tab,
    create_markdown_headings_tab,
    create_translation_tab,
    create_lists_to_headings_tab,
    create_integrated_tab,
    create_citation_merger_tab,
    create_citation_validator_tab,
    create_online_translation_tab,
)

# 直接在这个文件中创建demo对象
with gr.<PERSON><PERSON>(title="Deep Research 导出工具") as demo:
    gr.Markdown("# Deep Research 导出工具")

    with gr.Tabs() as tabs:
        # 加载整合工具箱（放在最前面）
        create_integrated_tab()

        # 加载各个独立标签页
        create_standard_export_tab()
        create_docx_export_tab()
        create_convert_to_docx_tab()
        create_markdown_headings_tab()
        create_citation_merger_tab()
        create_citation_validator_tab()
        create_lists_to_headings_tab()
        create_translation_tab()
        create_online_translation_tab()


def launch_ui():
    demo.launch()


if __name__ == "__main__":
    launch_ui()
