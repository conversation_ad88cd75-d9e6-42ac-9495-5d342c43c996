"""
日志模块，使用rich提供美观的日志输出
"""

import logging
import sys
from rich.console import Console
from rich.logging import RichHandler
from typing import Optional

# 创建rich控制台
console = Console()


def setup_logger(
    level: Optional[int] = None,
    name: Optional[str] = None,
    show_time: Optional[bool] = None,
    show_path: Optional[bool] = None,
    log_file: Optional[str] = None,
) -> logging.Logger:
    """
    配置并返回一个使用rich格式化的日志记录器

    Args:
        level: 日志级别，默认从配置读取
        name: 日志记录器名称，默认为None（根记录器）
        show_time: 是否显示时间戳，默认从配置读取
        show_path: 是否显示文件路径，默认从配置读取
        log_file: 日志文件路径，默认从配置读取

    Returns:
        配置好的日志记录器
    """
    # 从配置获取默认值
    try:
        from .config import get_logging_config
        config = get_logging_config()

        if level is None:
            level = getattr(logging, config.level.upper(), logging.INFO)
        if show_time is None:
            show_time = config.show_time
        if show_path is None:
            show_path = config.show_path
        if log_file is None:
            log_file = config.log_file
    except ImportError:
        # 如果配置模块不可用，使用默认值
        if level is None:
            level = logging.INFO
        if show_time is None:
            show_time = True
        if show_path is None:
            show_path = False

    # 配置处理器列表
    handlers = []

    # 配置rich控制台处理器
    rich_handler = RichHandler(
        console=console,
        rich_tracebacks=True,
        tracebacks_show_locals=False,
        show_time=show_time,
        show_path=show_path,
        markup=True,
    )
    handlers.append(rich_handler)

    # 如果指定了日志文件，添加文件处理器
    if log_file:
        from pathlib import Path
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        handlers.append(file_handler)

    # 配置日志格式
    log_format = "%(message)s"
    logging.basicConfig(
        level=level, format=log_format, datefmt="[%X]", handlers=handlers, force=True
    )

    # 获取指定名称的记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)

    return logger


# 默认记录器
logger = setup_logger(name="deepresearch_exporter")
