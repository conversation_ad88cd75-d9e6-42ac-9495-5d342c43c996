"""
引用验证器，整合原有的验证功能
"""

import re
import difflib
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


class CitationValidator:
    """引用验证器，负责验证Markdown文件中的引用"""

    def __init__(self):
        """初始化验证器"""
        pass

    def load_markdown_file(self, markdown_path: str) -> str:
        """
        加载 Markdown 文件内容

        Args:
            markdown_path: Markdown 文件路径

        Returns:
            Markdown 文件内容
        """
        try:
            with open(markdown_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取 Markdown 文件失败: {str(e)}")
            raise

    def extract_markdown_citations(self, markdown_text: str) -> Set[str]:
        """
        从 Markdown 文本中提取所有引用键

        Args:
            markdown_text: Markdown 文本内容

        Returns:
            Markdown 文本中的引用键集合
        """
        # 查找 Markdown 文本中的所有引用标记，格式为 [@citekey]
        cite_pattern = r"\[@([^\]]+)\]"
        cite_matches = re.finditer(cite_pattern, markdown_text)

        # 提取所有引用键
        cite_keys = {match.group(1) for match in cite_matches}

        return cite_keys

    def load_bibtex_keys(self, bibtex_path: str) -> Set[str]:
        """
        从 BibTeX 文件中加载所有引用键

        Args:
            bibtex_path: BibTeX 文件路径

        Returns:
            BibTeX 文件中的引用键集合
        """
        try:
            with open(bibtex_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 匹配 BibTeX 条目的正则表达式
            # 匹配 @类型{键, 的格式
            entry_pattern = r"@\w+\s*{\s*([^,]+)"
            entry_matches = re.finditer(entry_pattern, content)

            # 提取所有引用键
            bibtex_keys = {match.group(1).strip() for match in entry_matches}

            return bibtex_keys
        except Exception as e:
            logger.error(f"解析 BibTeX 文件失败: {str(e)}")
            return set()

    def find_similar_keys(
        self, missing_key: str, available_keys: Set[str], max_suggestions: int = 3
    ) -> List[str]:
        """
        为缺失的引用键查找相似的可用引用键

        Args:
            missing_key: 缺失的引用键
            available_keys: 可用的引用键集合
            max_suggestions: 最大建议数量

        Returns:
            相似引用键列表
        """
        if not available_keys:
            return []

        # 使用 difflib 计算字符串相似度
        similarities = []
        for key in available_keys:
            ratio = difflib.SequenceMatcher(None, missing_key.lower(), key.lower()).ratio()
            similarities.append((ratio, key))

        # 按相似度排序并返回前几个
        similarities.sort(reverse=True)
        return [key for _, key in similarities[:max_suggestions] if _ > 0.3]

    def validate_citations(
        self,
        markdown_path: str,
        bibtex_path: str
    ) -> Tuple[bool, List[str], Dict[str, List[str]]]:
        """
        验证Markdown文件中的引用

        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径

        Returns:
            (是否全部有效, 缺失的引用键列表, 相似引用键建议字典)
        """
        try:
            # 加载 Markdown 文件内容
            markdown_content = self.load_markdown_file(markdown_path)

            # 提取 Markdown 中的引用键
            markdown_citations = self.extract_markdown_citations(markdown_content)

            # 加载 BibTeX 中的引用键
            bibtex_keys = self.load_bibtex_keys(bibtex_path)

            # 查找缺失的引用键
            missing_keys = list(markdown_citations - bibtex_keys)

            # 为缺失的引用键查找相似的建议
            suggestions = {}
            for missing_key in missing_keys:
                similar_keys = self.find_similar_keys(missing_key, bibtex_keys)
                if similar_keys:
                    suggestions[missing_key] = similar_keys

            # 判断是否所有引用都有效
            all_valid = len(missing_keys) == 0

            logger.info(f"验证完成: {len(markdown_citations)} 个引用中有 {len(missing_keys)} 个缺失")

            return all_valid, missing_keys, suggestions

        except Exception as e:
            logger.error(f"验证引用时发生错误: {str(e)}")
            raise

    def highlight_missing_citations(
        self,
        markdown_path: str,
        bibtex_path: str,
        output_path: str
    ) -> Tuple[bool, List[str], str, Dict[str, List[str]]]:
        """
        高亮缺失的引用并输出到新文件

        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径
            output_path: 输出文件路径

        Returns:
            (是否全部有效, 缺失的引用键列表, 输出文件路径, 相似引用键建议字典)
        """
        try:
            # 验证引用
            all_valid, missing_keys, suggestions = self.validate_citations(markdown_path, bibtex_path)

            # 加载 Markdown 内容
            markdown_content = self.load_markdown_file(markdown_path)

            # 高亮缺失的引用
            highlighted_content = markdown_content
            for missing_key in missing_keys:
                # 将缺失的引用标记为红色
                pattern = rf"\[@{re.escape(missing_key)}\]"
                replacement = f"<mark style='background-color: #ffcccc'>[@{missing_key}]</mark>"
                highlighted_content = re.sub(pattern, replacement, highlighted_content)

            # 保存高亮后的内容
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(highlighted_content)

            logger.info(f"高亮文件已保存到: {output_path}")

            return all_valid, missing_keys, output_path, suggestions

        except Exception as e:
            logger.error(f"高亮缺失引用时发生错误: {str(e)}")
            raise

    def generate_report(
        self,
        missing_keys: List[str],
        suggestions: Dict[str, List[str]] = None
    ) -> str:
        """
        生成缺失引用的报告

        Args:
            missing_keys: 缺失的引用键列表
            suggestions: 相似引用键建议字典

        Returns:
            报告文本
        """
        if not missing_keys:
            return "✅ 所有引用都已找到，没有缺失的引用。"

        report_lines = [
            "# 引用验证报告",
            "",
            f"## 概要",
            f"- 缺失引用数量: {len(missing_keys)}",
            "",
            "## 缺失的引用键",
            ""
        ]

        for i, missing_key in enumerate(missing_keys, 1):
            report_lines.append(f"{i}. `{missing_key}`")

            # 添加建议
            if suggestions and missing_key in suggestions:
                similar_keys = suggestions[missing_key]
                if similar_keys:
                    report_lines.append("   - 可能的替代:")
                    for similar_key in similar_keys:
                        report_lines.append(f"     - `{similar_key}`")

            report_lines.append("")

        report_lines.extend([
            "## 建议",
            "1. 检查引用键的拼写是否正确",
            "2. 确认相关文献已添加到 BibTeX 文件中",
            "3. 如果有相似的引用键建议，请检查是否为同一文献",
            ""
        ])

        return "\n".join(report_lines)

    def replace_citation(self, markdown_text: str, old_key: str, new_key: str) -> str:
        """
        替换 Markdown 中的引用键

        Args:
            markdown_text: Markdown 文本
            old_key: 原引用键
            new_key: 新引用键

        Returns:
            替换后的 Markdown 文本
        """
        # 直接替换引用键
        pattern = r"\[@" + re.escape(old_key) + r"\]"
        replaced_text = re.sub(pattern, f"[@{new_key}]", markdown_text)

        # 替换可能存在的高亮标记的缺失引用（更强大的模式匹配）
        # 这会匹配[![@key] - 引用未找到!]形式和更复杂的格式
        highlight_pattern = r"\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\*"
        replaced_text = re.sub(highlight_pattern, f"[@{new_key}]", replaced_text)

        # 尝试匹配其他可能的高亮格式
        alt_pattern = r"\*\*\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\*\*\*"
        replaced_text = re.sub(alt_pattern, f"[@{new_key}]", replaced_text)

        # 匹配可能嵌套的高亮格式
        nested_pattern = r"\*\*\[\!\[@" + re.escape(old_key) + r"\].*?\]\*\* - [^\]]+\]"
        replaced_text = re.sub(nested_pattern, f"[@{new_key}]", replaced_text)

        return replaced_text

    def replace_citations_in_file(
        self, markdown_path: str, replacements: Dict[str, str], output_path: Optional[str] = None
    ) -> str:
        """
        在文件中批量替换引用键

        Args:
            markdown_path: Markdown 文件路径
            replacements: 替换字典 {原键: 新键}
            output_path: 输出文件路径

        Returns:
            输出文件路径
        """
        # 读取原文件
        with open(markdown_path, "r", encoding="utf-8") as f:
            markdown_text = f.read()

        # 逐个应用替换
        for old_key, new_key in replacements.items():
            markdown_text = self.replace_citation(markdown_text, old_key, new_key)

        # 如果没有指定输出路径，则覆盖原文件
        if output_path is None:
            output_path = markdown_path

        # 保存更新后的文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(markdown_text)

        return output_path
