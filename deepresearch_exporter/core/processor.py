"""
对话处理器，整合原有的解析功能
"""

import json
import os
import re
from typing import Dict, Any, Literal, Optional, Set, List, Tuple
from ..models import (
    Conversation,
    DeepResearchResponse,
    Message,
    Citation
)
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


class ConversationProcessor:
    """对话处理器，负责处理ChatGPT导出的对话文件"""

    def __init__(self):
        """初始化处理器"""
        pass

    def process_file(
        self,
        file_path: str,
        mode: Literal["citations", "content_references"] = "citations"
    ) -> DeepResearchResponse:
        """
        处理对话文件

        Args:
            file_path: 对话JSON文件路径
            mode: 处理模式

        Returns:
            处理后的Deep Research回复
        """
        return self.process_conversation(file_path, mode)

    def validate_file(self, file_path: str) -> bool:
        """
        验证文件是否为有效的ChatGPT导出文件

        Args:
            file_path: 文件路径

        Returns:
            是否为有效文件
        """
        try:
            self.load_conversation(file_path)
            return True
        except Exception:
            return False

    def load_conversation(self, file_path: str) -> Conversation:
        """
        从文件加载ChatGPT对话数据

        Args:
            file_path: JSON文件路径

        Returns:
            解析后的对话数据对象

        Raises:
            FileNotFoundError: 文件不存在
            json.JSONDecodeError: JSON解析错误
            ValueError: 数据格式错误
        """
        logger.debug(f"加载对话文件: {file_path}")
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
                    raise ValueError(f"文件不是有效的JSON格式: {str(e)}")

            logger.debug("对话文件加载成功")

            # 基本格式验证
            if not isinstance(data, dict):
                logger.error("对话数据不是字典格式")
                raise ValueError("无效的对话数据格式：期望一个JSON对象，但得到了其他类型")

            # 检查必要的字段
            required_fields = ["mapping", "title"]
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                logger.error(f"对话数据缺少必要字段: {missing_fields}")
                raise ValueError(
                    f"对话数据格式不完整，缺少字段: {', '.join(missing_fields)}"
                )

            # 将字典转换为Conversation对象
            try:
                conversation = Conversation.model_validate(data)
                logger.debug(f"成功解析对话数据，包含 {len(conversation.mapping)} 个节点")
                return conversation
            except Exception as e:
                logger.error(f"解析对话数据失败: {str(e)}")
                raise ValueError(f"无法解析对话数据: {str(e)}")

        except FileNotFoundError:
            logger.error(f"文件不存在: {file_path}")
            raise FileNotFoundError(f"文件不存在: {file_path}")
        except Exception as e:
            if not isinstance(e, (ValueError, FileNotFoundError)):
                logger.error(f"加载对话文件时发生未知错误: {str(e)}")
                raise ValueError(f"加载对话数据时发生错误: {str(e)}")
            raise

    def is_deep_research_message(self, message: Message) -> bool:
        """
        判断消息是否为Deep Research模式的回复

        Args:
            message: 消息对象

        Returns:
            是否为Deep Research模式
        """
        # 检查消息元数据中是否包含citations或content_references字段，这是Deep Research模式的特征
        if message.metadata and (
            message.metadata.get("citations") or message.metadata.get("content_references")
        ):
            logger.debug("检测到消息包含citations或content_references字段")
            return True

        # 检查消息元数据中是否有research模型标记
        if message.metadata and message.metadata.get("model_slug") == "research":
            logger.debug("检测到消息使用research模型")
            return True

        # 检查是否有is_async_task_result_message标记，这通常表示Deep Research结果
        if message.metadata and message.metadata.get("is_async_task_result_message"):
            logger.debug("检测到消息是异步任务结果")
            return True

        return False

    def process_conversation(
        self, file_path: str, mode: Literal["citations", "content_references"] = "citations"
    ) -> DeepResearchResponse:
        """
        处理ChatGPT Deep Research对话，提取内容和引用

        Args:
            file_path: 对话JSON文件路径
            mode: 处理模式，'citations'使用引用标记处理，'content_references'使用内容引用处理

        Returns:
            处理后的Deep Research响应对象
        """
        try:
            # 检查文件路径是否有效
            if not file_path or not os.path.exists(file_path):
                logger.error(f"文件路径无效或不存在: {file_path}")
                raise FileNotFoundError(f"文件路径无效或不存在: {file_path}")

            # 加载对话
            conversation = self.load_conversation(file_path)

            # 提取Deep Research内容
            response = self.extract_deep_research_content(conversation, mode=mode)

            if not response:
                logger.warning("未找到Deep Research内容，返回空响应")
                return DeepResearchResponse(content="", citations=[])

            logger.info(f"成功提取Deep Research内容，包含 {len(response.citations)} 条引用")
            return response

        except Exception as e:
            logger.error(f"处理对话失败: {str(e)}", exc_info=True)
            raise

    def extract_deep_research_content(
        self, conversation: Conversation, mode: str = "citations"
    ) -> Optional[DeepResearchResponse]:
        """
        从对话中提取Deep Research模式的回复内容

        Args:
            conversation: 对话数据
            mode: 引用处理模式，可选值为"citations"或"content_references"，默认为"citations"

        Returns:
            Deep Research回复内容，如果未找到则返回None
        """
        # 从当前节点开始回溯，查找Deep Research回复
        current_node_id = conversation.current_node
        visited: Set[str] = set()

        while current_node_id and current_node_id not in visited:
            visited.add(current_node_id)

            if current_node_id not in conversation.mapping:
                break

            node = conversation.mapping[current_node_id]
            if node.message and self.is_deep_research_message(node.message):
                # 找到Deep Research回复
                message = node.message

                # 提取内容
                content = message.content.get_text()

                # 使用直接引用处理方法
                if message.metadata:
                    if mode == "citations" and message.metadata.get("citations"):
                        processed_content, citations = (
                            self.process_content_with_direct_references(
                                message, message.metadata["citations"]
                            )
                        )
                        return DeepResearchResponse(
                            content=processed_content, citations=citations
                        )
                    elif mode == "content_references" and message.metadata.get("content_references"):
                        processed_content, citations = (
                            self.process_content_with_content_references(
                                message, message.metadata["content_references"]
                            )
                        )
                        return DeepResearchResponse(
                            content=processed_content, citations=citations
                        )

                # 如果没有元数据或者指定的引用类型不存在，返回原始内容
                logger.warning(f"未找到{mode}数据，返回原始内容")
                return DeepResearchResponse(content=content, citations=[])

            # 移动到父节点
            current_node_id = node.parent

        logger.warning("未找到Deep Research回复")
        return None

    def generate_cite_key(
        self, title: str, authors: list = None, year: int = None, index: int = 0
    ) -> str:
        """
        生成引用键

        Args:
            title: 文献标题
            authors: 作者列表
            year: 发表年份
            index: 引用索引

        Returns:
            生成的引用键
        """
        if not authors:
            authors = []

        # 尝试基于作者+年份生成键
        if authors and len(authors) > 0 and year:
            # 提取第一个作者姓氏
            first_author = (
                authors[0].split()[-1].lower() if " " in authors[0] else authors[0].lower()
            )
            # 去掉非字母字符
            first_author = "".join(c for c in first_author if c.isalpha())
            if first_author and len(first_author) > 0:
                return f"{first_author}{year}"

        # 如果无法从作者+年份生成，尝试从标题生成
        if title and len(title) > 0:
            # 提取标题中的关键词
            keywords = [
                word.lower()
                for word in re.findall(r"\b\w+\b", title)
                if len(word) > 3
                and word.lower()
                not in [
                    "the", "and", "for", "are", "but", "not", "you", "all", "can", "had",
                    "her", "was", "one", "our", "out", "day", "get", "has", "him", "his",
                    "how", "its", "may", "new", "now", "old", "see", "two", "who", "boy",
                    "did", "she", "use", "way", "what", "when", "with", "have", "from",
                    "they", "know", "want", "been", "good", "much", "some", "time", "very",
                    "will", "about", "after", "first", "never", "other", "right", "where",
                    "these", "think", "would", "could", "should", "through", "between"
                ]
            ]
            if keywords:
                # 取前两个关键词
                key_part = "".join(keywords[:2])
                if year:
                    return f"{key_part}{year}"
                else:
                    return f"{key_part}{index + 1:02d}"

        # 最后的备选方案：使用索引
        return f"ref{index + 1:02d}"

    def process_content_with_direct_references(
        self, message: Message, citations_data: List[Dict]
    ) -> Tuple[str, List[Citation]]:
        """
        使用直接引用数据处理内容

        Args:
            message: 消息对象
            citations_data: 引用数据列表

        Returns:
            处理后的内容和引用列表
        """
        from ._processor_content import process_content_with_direct_references
        return process_content_with_direct_references(self, message, citations_data)

    def process_content_with_content_references(
        self, message: Message, content_references: List[Dict]
    ) -> Tuple[str, List[Citation]]:
        """
        使用内容引用数据处理内容

        Args:
            message: 消息对象
            content_references: 内容引用数据列表

        Returns:
            处理后的内容和引用列表
        """
        from ._processor_content import process_content_with_content_references
        return process_content_with_content_references(self, message, content_references)
