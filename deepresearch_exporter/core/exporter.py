"""
文档导出器，整合原有的导出功能
"""

import os
from typing import Optional, Tuple
from ..models import DeepResearchResponse
from ..exporter import (
    export_response as _export_response,
    export_response_with_docx as _export_response_with_docx,
    export_docx as _export_docx
)


class DocumentExporter:
    """文档导出器，负责将处理后的内容导出为各种格式"""
    
    def __init__(self, output_dir: str = "output"):
        """
        初始化导出器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def export_markdown_and_bibtex(
        self, 
        response: DeepResearchResponse, 
        base_name: str
    ) -> Tuple[str, str]:
        """
        导出Markdown和BibTeX文件
        
        Args:
            response: 处理后的回复
            base_name: 基本文件名
            
        Returns:
            (markdown文件路径, bibtex文件路径)
        """
        return _export_response(response, self.output_dir, base_name)
    
    def export_with_docx(
        self,
        response: DeepResearchResponse,
        base_name: str,
        csl_path: Optional[str] = None,
        reference_doc: Optional[str] = None
    ) -> Tuple[str, str, str]:
        """
        导出Markdown、BibTeX和DOCX文件
        
        Args:
            response: 处理后的回复
            base_name: 基本文件名
            csl_path: CSL样式文件路径
            reference_doc: Word参考文档路径
            
        Returns:
            (markdown文件路径, bibtex文件路径, docx文件路径)
        """
        return _export_response_with_docx(
            response, self.output_dir, base_name, csl_path, reference_doc
        )
    
    def convert_to_docx(
        self,
        markdown_path: str,
        bibtex_path: str,
        output_path: str,
        csl_path: Optional[str] = None,
        reference_doc: Optional[str] = None
    ) -> str:
        """
        将Markdown和BibTeX转换为DOCX
        
        Args:
            markdown_path: Markdown文件路径
            bibtex_path: BibTeX文件路径
            output_path: 输出DOCX文件路径
            csl_path: CSL样式文件路径
            reference_doc: Word参考文档路径
            
        Returns:
            DOCX文件路径
        """
        return _export_docx(markdown_path, bibtex_path, output_path, csl_path, reference_doc)
