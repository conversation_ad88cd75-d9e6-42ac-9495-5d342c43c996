"""
内容处理功能的扩展模块
"""

import re
from typing import List, Tuple, Dict, Set
from ..models import Message, Citation
from ..logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def process_content_with_direct_references(
    processor, message: Message, citations_data: List[Dict]
) -> Tuple[str, List[Citation]]:
    """
    使用直接引用数据处理内容
    
    Args:
        processor: ConversationProcessor实例
        message: 消息对象
        citations_data: 引用数据列表
        
    Returns:
        处理后的内容和引用列表
    """
    content = message.content.get_text()
    citations = []
    used_keys: Set[str] = set()

    logger.debug(f"处理 {len(citations_data)} 条引用")

    for i, citation_data in enumerate(citations_data):
        try:
            # 提取引用信息
            title = citation_data.get("title", "")
            url = citation_data.get("url", "")
            authors = citation_data.get("authors", [])
            year = citation_data.get("year")
            
            # 生成唯一的引用键
            base_key = processor.generate_cite_key(title, authors, year, i)
            cite_key = base_key
            counter = 1
            while cite_key in used_keys:
                cite_key = f"{base_key}_{counter}"
                counter += 1
            used_keys.add(cite_key)

            # 创建Citation对象
            citation = Citation(
                key=cite_key,
                title=title,
                url=url,
                authors=authors,
                year=year,
                source=citation_data.get("source", ""),
                access_date=citation_data.get("access_date", ""),
                publisher=citation_data.get("publisher", ""),
                journal=citation_data.get("journal", ""),
                volume=citation_data.get("volume", ""),
                issue=citation_data.get("issue", ""),
                pages=citation_data.get("pages", ""),
                doi=citation_data.get("doi", ""),
                isbn=citation_data.get("isbn", ""),
                note=citation_data.get("note", ""),
            )
            citations.append(citation)

            logger.debug(f"处理引用: {cite_key} - {title}")

        except Exception as e:
            logger.warning(f"处理第 {i+1} 条引用时出错: {str(e)}")
            continue

    # 在内容中查找并替换引用标记
    processed_content = replace_citation_markers(content, citations)
    
    return processed_content, citations


def process_content_with_content_references(
    processor, message: Message, content_references: List[Dict]
) -> Tuple[str, List[Citation]]:
    """
    使用内容引用数据处理内容
    
    Args:
        processor: ConversationProcessor实例
        message: 消息对象
        content_references: 内容引用数据列表
        
    Returns:
        处理后的内容和引用列表
    """
    content = message.content.get_text()
    citations = []
    used_keys: Set[str] = set()

    logger.debug(f"处理 {len(content_references)} 条内容引用")

    for i, ref_data in enumerate(content_references):
        try:
            # 从content_references中提取信息
            title = ref_data.get("title", "")
            url = ref_data.get("url", "")
            
            # 生成唯一的引用键
            base_key = processor.generate_cite_key(title, [], None, i)
            cite_key = base_key
            counter = 1
            while cite_key in used_keys:
                cite_key = f"{base_key}_{counter}"
                counter += 1
            used_keys.add(cite_key)

            # 创建Citation对象
            citation = Citation(
                key=cite_key,
                title=title,
                url=url,
                source=ref_data.get("source", ""),
                access_date=ref_data.get("access_date", ""),
            )
            citations.append(citation)

            logger.debug(f"处理内容引用: {cite_key} - {title}")

        except Exception as e:
            logger.warning(f"处理第 {i+1} 条内容引用时出错: {str(e)}")
            continue

    # 在内容中查找并替换引用标记
    processed_content = replace_citation_markers(content, citations)
    
    return processed_content, citations


def replace_citation_markers(content: str, citations: List[Citation]) -> str:
    """
    在内容中替换引用标记
    
    Args:
        content: 原始内容
        citations: 引用列表
        
    Returns:
        处理后的内容
    """
    processed_content = content
    
    # 查找所有可能的引用标记模式
    citation_patterns = [
        r'\[(\d+)\]',  # [1], [2], etc.
        r'\((\d+)\)',  # (1), (2), etc.
        r'【(\d+)】',   # 【1】, 【2】, etc.
    ]
    
    for pattern in citation_patterns:
        matches = re.finditer(pattern, processed_content)
        for match in reversed(list(matches)):  # 从后往前替换，避免位置偏移
            try:
                citation_num = int(match.group(1))
                if 1 <= citation_num <= len(citations):
                    citation = citations[citation_num - 1]
                    # 替换为Markdown链接格式
                    if citation.url:
                        replacement = f"[{citation.title}]({citation.url})"
                    else:
                        replacement = f"[{citation.title}]"
                    
                    start, end = match.span()
                    processed_content = (
                        processed_content[:start] + 
                        replacement + 
                        processed_content[end:]
                    )
            except (ValueError, IndexError):
                continue
    
    return processed_content
