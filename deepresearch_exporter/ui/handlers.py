"""
处理函数模块，包含UI界面的后端处理逻辑
"""

import os
import tempfile
from pathlib import Path
from typing import Tuple, Literal, Optional, List, Dict, Any, Union

from ..core.processor import ConversationProcessor
from ..exporter import export_response, export_response_with_docx, export_docx
from ..logger import setup_logger
from ..models import TranslationConfig
from ..translator import Translator
from ..online_translator import (
    OnlineTranslator,
    OnlineTranslationConfig,
    TranslatorType,
)

# 配置日志
logger = setup_logger(name=__name__)


def process_file(
    file_obj: Union[tempfile._TemporaryFileWrapper, str],
    output_name: str,
    mode: Literal["citations", "content_references"],
) -> Tuple[str, str, str]:
    """
    处理上传的文件并返回处理结果

    Args:
        file_obj: 上传的临时文件对象或文件路径
        output_name: 输出文件名（不含扩展名）
        mode: 处理模式

    Returns:
        (Markdown内容, BibTeX内容, 处理结果信息)
    """
    try:
        # 检查file_obj是否为None或空字符串
        if file_obj is None or (isinstance(file_obj, str) and not file_obj):
            return "", "", "错误：未提供文件或文件上传失败"

        # 获取文件路径
        file_path = file_obj if isinstance(file_obj, str) else file_obj.name

        # 检查文件路径是否有效
        if not file_path or not os.path.exists(file_path):
            return "", "", f"错误：文件路径 '{file_path}' 不存在或无效"

        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 处理对话
            processor = ConversationProcessor()
            response = processor.process_conversation(file_path, mode=mode)

            # 如果未提供输出名称，使用上传文件名
            if not output_name:
                output_name = Path(file_path).stem

            # 导出结果
            md_path, bib_path = export_response(response, temp_dir, output_name)

            # 读取生成的文件内容
            with open(md_path, "r", encoding="utf-8") as f:
                md_content = f.read()

            with open(bib_path, "r", encoding="utf-8") as f:
                bib_content = f.read()

            result_info = f"成功处理！提取了 {len(response.citations)} 条引用。"
            return md_content, bib_content, result_info

    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        return "", "", f"处理失败: {str(e)}"


def process_file_with_docx(
    file_obj: Union[tempfile._TemporaryFileWrapper, str],
    output_name: str,
    mode: Literal["citations", "content_references"],
    csl_file: Optional[Union[tempfile._TemporaryFileWrapper, str]] = None,
    ref_doc_file: Optional[Union[tempfile._TemporaryFileWrapper, str]] = None,
) -> Tuple[str, str, str, str, str]:
    """
    处理上传的文件并返回处理结果，包括DOCX导出

    Args:
        file_obj: 上传的临时文件对象或文件路径
        output_name: 输出文件名（不含扩展名）
        mode: 处理模式
        csl_file: CSL样式文件（可选）或文件路径
        ref_doc_file: 参考文档文件（可选）或文件路径

    Returns:
        (Markdown内容, BibTeX内容, DOCX文件路径, 处理结果信息, DOCX下载文件名)
    """
    try:
        # 检查file_obj是否为None或空字符串
        if file_obj is None or (isinstance(file_obj, str) and not file_obj):
            return "", "", None, "错误：未提供文件或文件上传失败", ""

        # 获取文件路径
        file_path = file_obj if isinstance(file_obj, str) else file_obj.name

        # 检查文件路径是否有效
        if not file_path or not os.path.exists(file_path):
            return "", "", None, f"错误：文件路径 '{file_path}' 不存在或无效", ""

        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 处理对话
            processor = ConversationProcessor()
            response = processor.process_conversation(file_path, mode=mode)

            # 如果未提供输出名称，使用上传文件名
            if not output_name:
                output_name = Path(file_path).stem

            # 导出结果
            csl_path = (
                csl_file.name
                if isinstance(csl_file, tempfile._TemporaryFileWrapper)
                else csl_file
            )
            ref_doc_path = (
                ref_doc_file.name
                if isinstance(ref_doc_file, tempfile._TemporaryFileWrapper)
                else ref_doc_file
            )

            md_path, bib_path, docx_path = export_response_with_docx(
                response,
                temp_dir,
                output_name,
                csl_path=csl_path,
                reference_doc=ref_doc_path,
            )

            # 读取生成的文件内容
            with open(md_path, "r", encoding="utf-8") as f:
                md_content = f.read()

            with open(bib_path, "r", encoding="utf-8") as f:
                bib_content = f.read()

            # 复制DOCX文件到一个持久的临时文件
            persistent_docx = tempfile.NamedTemporaryFile(
                delete=False, suffix=".docx"
            ).name
            with open(docx_path, "rb") as src, open(persistent_docx, "wb") as dst:
                dst.write(src.read())

            result_info = (
                f"成功处理！提取了 {len(response.citations)} 条引用，并导出为DOCX格式。"
            )
            return (
                md_content,
                bib_content,
                persistent_docx,
                result_info,
                f"{output_name}.docx",
            )

    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        return "", "", None, f"处理失败: {str(e)}", ""


def convert_to_docx(
    md_file: Union[tempfile._TemporaryFileWrapper, str],
    bib_file: Union[tempfile._TemporaryFileWrapper, str],
    output_name: str,
    csl_file: Optional[Union[tempfile._TemporaryFileWrapper, str]] = None,
    ref_doc_file: Optional[Union[tempfile._TemporaryFileWrapper, str]] = None,
) -> Tuple[str, str]:
    """
    将上传的Markdown和BibTeX文件转换为DOCX

    Args:
        md_file: Markdown文件或文件路径
        bib_file: BibTeX文件或文件路径
        output_name: 输出文件名（不含扩展名）
        csl_file: CSL样式文件（可选）或文件路径
        ref_doc_file: 参考文档文件（可选）或文件路径

    Returns:
        (DOCX文件路径, 处理结果信息)
    """
    try:
        # 检查必要的文件是否为None或空字符串
        if md_file is None or (isinstance(md_file, str) and not md_file):
            return None, "错误：未提供Markdown文件或文件上传失败"
        if bib_file is None or (isinstance(bib_file, str) and not bib_file):
            return None, "错误：未提供BibTeX文件或文件上传失败"

        # 获取文件路径
        md_path = md_file if isinstance(md_file, str) else md_file.name
        bib_path = bib_file if isinstance(bib_file, str) else bib_file.name

        # 检查文件路径是否有效
        if not md_path or not os.path.exists(md_path):
            return None, f"错误：Markdown文件路径 '{md_path}' 不存在或无效"
        if not bib_path or not os.path.exists(bib_path):
            return None, f"错误：BibTeX文件路径 '{bib_path}' 不存在或无效"

        # 如果未提供输出名称，使用上传文件名
        if not output_name:
            output_name = Path(md_path).stem

        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            docx_path = os.path.join(temp_dir, f"{output_name}.docx")

            # 导出DOCX
            csl_path = (
                csl_file
                if isinstance(csl_file, str)
                else (csl_file.name if csl_file else None)
            )
            ref_doc_path = (
                ref_doc_file
                if isinstance(ref_doc_file, str)
                else (ref_doc_file.name if ref_doc_file else None)
            )

            export_docx(
                md_path,
                bib_path,
                docx_path,
                csl_path=csl_path,
                reference_doc=ref_doc_path,
            )

            # 复制DOCX文件到一个持久的临时文件
            persistent_docx = tempfile.NamedTemporaryFile(
                delete=False, suffix=".docx"
            ).name
            with open(docx_path, "rb") as src, open(persistent_docx, "wb") as dst:
                dst.write(src.read())

            result_info = f"成功将Markdown和BibTeX转换为DOCX格式。"
            return persistent_docx, result_info

    except Exception as e:
        logger.error(f"处理失败: {str(e)}", exc_info=True)
        return None, f"处理失败: {str(e)}"


def download_files(
    md_content: str, bib_content: str, output_name: str
) -> Tuple[str, str]:
    """
    创建并返回可下载的文件

    Args:
        md_content: Markdown内容
        bib_content: BibTeX内容
        output_name: 输出文件名（不含扩展名）

    Returns:
        (Markdown文件路径, BibTeX文件路径)
    """
    if not output_name:
        output_name = "deepresearch_export"

    # 创建临时文件
    md_path = tempfile.NamedTemporaryFile(delete=False, suffix=".md").name
    bib_path = tempfile.NamedTemporaryFile(delete=False, suffix=".bib").name

    # 写入内容
    with open(md_path, "w", encoding="utf-8") as f:
        f.write(md_content)

    with open(bib_path, "w", encoding="utf-8") as f:
        f.write(bib_content)

    return md_path, bib_path


def translate_text(
    text: str,
    base_url: str = "http://localhost:11434/v1",
    api_key: str = "ollama",
    model: str = "qwen2.5:32b",
    source_language: str = "English",
    target_language: str = "Chinese",
    domain: Optional[str] = None,
    temperature: float = 0.3,
    top_p: float = 0.9,
) -> Tuple[str, str]:
    """
    翻译单个文本

    Args:
        text: 要翻译的文本
        base_url: API基础URL
        api_key: API密钥
        model: 模型名称
        source_language: 源语言
        target_language: 目标语言
        domain: 专业领域
        temperature: 温度参数
        top_p: top-p参数

    Returns:
        (翻译结果, 错误信息)
    """
    if not text.strip():
        return "", "请输入要翻译的文本"

    config = TranslationConfig(
        base_url=base_url,
        api_key=api_key,
        model=model,
        source_language=source_language,
        target_language=target_language,
        domain=domain,
        temperature=temperature,
        top_p=top_p,
    )

    translator = Translator(config)
    result = translator.translate_text(text)

    if result.error:
        return "", f"翻译失败: {result.error}"

    return result.translation, ""


def translate_file_content(
    file_path: str,
    base_url: str = "http://localhost:11434/v1",
    api_key: str = "ollama",
    model: str = "qwen2.5:32b",
    source_language: str = "English",
    target_language: str = "Chinese",
    domain: Optional[str] = None,
    temperature: float = 0.3,
    top_p: float = 0.9,
    batch_size: int = 5,
    max_tokens: int = 4000,
    max_requests: int = 5,
    output_name: Optional[str] = None,
) -> Tuple[str, str, str, str]:
    """
    翻译文件内容

    Args:
        file_path: 文件路径
        base_url: API基础URL
        api_key: API密钥
        model: 模型名称
        source_language: 源语言
        target_language: 目标语言
        domain: 专业领域
        temperature: 温度参数
        top_p: top-p参数
        batch_size: 批处理大小
        max_tokens: 最大token数
        max_requests: 最大请求数
        output_name: 输出文件名（不含扩展名）

    Returns:
        (原始文本, 翻译后文本, 翻译后的文件路径, 错误信息)
    """
    if not file_path:
        return "", "", "", "请上传文件"

    try:
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 配置翻译器
        config = TranslationConfig(
            base_url=base_url,
            api_key=api_key,
            model=model,
            source_language=source_language,
            target_language=target_language,
            domain=domain,
            temperature=temperature,
            top_p=top_p,
            batch_size=batch_size,
            max_tokens=max_tokens,
            max_requests=max_requests,
        )

        translator = Translator(config)

        # 翻译内容
        result = translator.translate_long_text(content)

        if result.error:
            return content, "", "", f"翻译失败: {result.error}"

        # 确定输出文件名
        if not output_name:
            base, ext = os.path.splitext(os.path.basename(file_path))
            output_name = f"{base}_translated"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(
            suffix=os.path.splitext(file_path)[1],
            prefix=f"{output_name}_",
            delete=False,
            mode="w",
            encoding="utf-8",
        ) as tmp:
            tmp.write(result.translation)
            output_path = tmp.name

        return content, result.translation, output_path, ""

    except Exception as e:
        return "", "", "", f"处理文件时出错: {str(e)}"


def online_translate_text(
    text: str,
    translator_type: str = "google",
    api_key: str = "",
    source_language: Optional[str] = None,
    target_language: str = "zh-CN",
    formality: Optional[str] = None,
    preserve_formatting: bool = False,
    split_sentences: str = "nonewlines",
    tag_handling: Optional[str] = None,
    model_type: Optional[str] = None,
) -> Tuple[str, str]:
    """
    使用在线翻译API翻译单个文本

    Args:
        text: 要翻译的文本
        translator_type: 翻译器类型，"google"或"deepl"
        api_key: API密钥（DeepL需要）
        source_language: 源语言
        target_language: 目标语言
        formality: 语言风格（DeepL特有），可选："default", "more", "less"
        preserve_formatting: 是否保留格式（DeepL特有）
        split_sentences: 分句策略（DeepL特有），可选："nonewlines", "default", "no"
        tag_handling: 标签处理方式（DeepL特有），可选："xml", "html"
        model_type: 模型类型（DeepL特有），可选："quality_optimized", "prefer_quality_optimized"

    Returns:
        (翻译结果, 错误信息)
    """
    if not text.strip():
        return "", "请输入要翻译的文本"

    try:
        # 转换translator_type为枚举类型
        translator_enum = (
            TranslatorType.GOOGLE
            if translator_type.lower() == "google"
            else TranslatorType.DEEPL
        )

        # 源语言处理 - 如果是"auto"，设为None以便DeepL自动检测
        if source_language == "auto":
            source_language = None

        config = OnlineTranslationConfig(
            translator_type=translator_enum,
            api_key=api_key,
            source_language=source_language,
            target_language=target_language,
            formality=formality,
            preserve_formatting=preserve_formatting,
            split_sentences=split_sentences,
            tag_handling=tag_handling,
            model_type=model_type,
        )

        translator = OnlineTranslator(config)
        result = translator.translate_text(text)

        if result.error:
            return "", f"翻译失败: {result.error}"

        return result.translation, ""

    except Exception as e:
        logger.error(f"在线翻译失败: {str(e)}")
        return "", f"在线翻译发生错误: {str(e)}"


def online_translate_file_content(
    file_path: str,
    translator_type: str = "google",
    api_key: str = "",
    source_language: Optional[str] = None,
    target_language: str = "zh-CN",
    formality: Optional[str] = None,
    preserve_formatting: bool = False,
    split_sentences: str = "nonewlines",
    tag_handling: Optional[str] = None,
    model_type: Optional[str] = None,
    output_name: Optional[str] = None,
) -> Tuple[str, str, str, str]:
    """
    使用在线翻译API翻译文件内容

    Args:
        file_path: 文件路径
        translator_type: 翻译器类型，"google"或"deepl"
        api_key: API密钥（DeepL需要）
        source_language: 源语言
        target_language: 目标语言
        formality: 语言风格（DeepL特有），可选："default", "more", "less"
        preserve_formatting: 是否保留格式（DeepL特有）
        split_sentences: 分句策略（DeepL特有），可选："nonewlines", "default", "no"
        tag_handling: 标签处理方式（DeepL特有），可选："xml", "html"
        model_type: 模型类型（DeepL特有），可选："quality_optimized", "prefer_quality_optimized"
        output_name: 输出文件名（不含扩展名）

    Returns:
        (原始文本, 翻译后文本, 翻译后的文件路径, 错误信息)
    """
    if not file_path:
        return "", "", "", "请上传文件"

    try:
        # 读取文件内容
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 转换translator_type为枚举类型
        translator_enum = (
            TranslatorType.GOOGLE
            if translator_type.lower() == "google"
            else TranslatorType.DEEPL
        )

        # 源语言处理 - 如果是"auto"，设为None以便DeepL自动检测
        if source_language == "auto":
            source_language = None

        # 配置翻译器
        config = OnlineTranslationConfig(
            translator_type=translator_enum,
            api_key=api_key,
            source_language=source_language,
            target_language=target_language,
            formality=formality,
            preserve_formatting=preserve_formatting,
            split_sentences=split_sentences,
            tag_handling=tag_handling,
            model_type=model_type,
        )

        translator = OnlineTranslator(config)

        # 翻译内容
        result = translator.translate_long_text(content)

        if result.error:
            return content, "", "", f"翻译失败: {result.error}"

        # 确定输出文件名
        if not output_name:
            base, ext = os.path.splitext(os.path.basename(file_path))
            output_name = f"{base}_translated"

        # 创建临时文件
        with tempfile.NamedTemporaryFile(
            suffix=os.path.splitext(file_path)[1],
            prefix=f"{output_name}_",
            delete=False,
            mode="w",
            encoding="utf-8",
        ) as tmp:
            tmp.write(result.translation)
            output_path = tmp.name

        return content, result.translation, output_path, ""

    except Exception as e:
        logger.error(f"处理文件时出错: {str(e)}")
        return "", "", "", f"处理文件时出错: {str(e)}"
