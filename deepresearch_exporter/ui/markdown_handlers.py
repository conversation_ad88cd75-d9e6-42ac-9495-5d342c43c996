"""
Markdown处理工具的UI处理函数
"""

import os
import tempfile
from typing import Dict, List, Optional, Tuple, Union

import gradio as gr

from deepresearch_exporter.tools.markdown import MarkdownProcessor


def process_markdown_headings(
    markdown_file: Optional[gr.File],
    markdown_text: str,
    adjustment: int,
    first_level_as_title: bool,
    min_level: int,
    max_level: int,
    output_name: Optional[str] = None,
) -> Tuple[str, Optional[str], str]:
    """
    处理Markdown文件或文本中的标题层级

    参数:
        markdown_file: Markdown文件
        markdown_text: Markdown文本（当未提供文件时使用）
        adjustment: 标题级别的调整量
        first_level_as_title: 是否将第一个一级标题保留为文章标题
        min_level: 允许的最小标题级别
        max_level: 允许的最大标题级别
        output_name: 输出文件名（可选）

    返回:
        处理后的Markdown内容，下载文件路径（如果有），处理结果信息
    """
    content = ""

    # 获取内容
    if markdown_file and markdown_file.name:
        try:
            with open(markdown_file.name, "r", encoding="utf-8") as f:
                content = f.read()
                # 如果未提供输出文件名，则使用原文件名
                if not output_name:
                    output_name = (
                        os.path.splitext(os.path.basename(markdown_file.name))[0]
                        + "_adjusted"
                    )
        except Exception as e:
            return "", None, f"读取文件时出错: {str(e)}"
    elif markdown_text:
        content = markdown_text
        # 如果未提供输出文件名，则使用默认名称
        if not output_name:
            output_name = "adjusted_markdown"
    else:
        return "", None, "请提供Markdown文件或文本内容"

    # 处理标题
    try:
        markdown_processor = MarkdownProcessor()
        processed_content = markdown_processor.adjust_heading_levels(
            content, adjustment, first_level_as_title, min_level, max_level
        )

        # 创建输出文件
        output_file = None
        if content:
            temp_dir = tempfile.gettempdir()
            output_file_path = os.path.join(temp_dir, f"{output_name}.md")

            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write(processed_content)

            return (
                processed_content,
                output_file_path,
                f"处理成功，已调整标题层级（调整量: {adjustment}）",
            )

        return processed_content, None, "处理成功，但无内容可供下载"

    except Exception as e:
        return "", None, f"处理Markdown时出错: {str(e)}"


def analyze_markdown_headings(
    markdown_file: Optional[gr.File], markdown_text: str
) -> Tuple[str, str]:
    """
    分析Markdown文件或文本中的标题结构

    参数:
        markdown_file: Markdown文件
        markdown_text: Markdown文本（当未提供文件时使用）

    返回:
        分析结果，处理结果信息
    """
    content = ""

    # 获取内容
    if markdown_file and markdown_file.name:
        try:
            with open(markdown_file.name, "r", encoding="utf-8") as f:
                content = f.read()
        except Exception as e:
            return "", f"读取文件时出错: {str(e)}"
    elif markdown_text:
        content = markdown_text
    else:
        return "", "请提供Markdown文件或文本内容"

    # 分析标题结构
    try:
        markdown_processor = MarkdownProcessor()
        headings = markdown_processor.extract_heading_structure(content)

        if not headings:
            return "", "未在文档中找到标题"

        # 生成报告
        report = "# 标题结构分析\n\n"
        report += f"文档中共有 {len(headings)} 个标题\n\n"

        # 按级别统计
        level_counts = {}
        for h in headings:
            level = h["level"]
            if level not in level_counts:
                level_counts[level] = 0
            level_counts[level] += 1

        report += "## 各级标题数量\n\n"
        for level in sorted(level_counts.keys()):
            report += f"- {level} 级标题: {level_counts[level]} 个\n"

        report += "\n## 标题列表\n\n"
        for i, h in enumerate(headings, 1):
            indent = "    " * (h["level"] - 1)
            report += f"{i}. {indent}{h['content']} (Level {h['level']})\n"

        return report, f"分析完成，共找到 {len(headings)} 个标题"

    except Exception as e:
        return "", f"分析Markdown时出错: {str(e)}"


def process_lists_to_headings(
    markdown_file: Optional[gr.File],
    markdown_text: str,
    output_name: Optional[str] = None,
) -> Tuple[str, Optional[str], str]:
    """
    处理Markdown文件或文本中的无序列表，将其转换为多级标题

    参数:
        markdown_file: Markdown文件
        markdown_text: Markdown文本（当未提供文件时使用）
        output_name: 输出文件名（可选）

    返回:
        处理后的Markdown内容，下载文件路径（如果有），处理结果信息
    """
    content = ""

    # 获取内容
    if markdown_file and markdown_file.name:
        try:
            with open(markdown_file.name, "r", encoding="utf-8") as f:
                content = f.read()
                # 如果未提供输出文件名，则使用原文件名
                if not output_name:
                    output_name = (
                        os.path.splitext(os.path.basename(markdown_file.name))[0]
                        + "_converted"
                    )
        except Exception as e:
            return "", None, f"读取文件时出错: {str(e)}"
    elif markdown_text:
        content = markdown_text
        # 如果未提供输出文件名，则使用默认名称
        if not output_name:
            output_name = "converted_markdown"
    else:
        return "", None, "请提供Markdown文件或文本内容"

    # 处理无序列表
    try:
        markdown_processor = MarkdownProcessor()
        processed_content = markdown_processor.convert_lists_to_headings(content)

        # 创建输出文件
        output_file = None
        if content:
            temp_dir = tempfile.gettempdir()
            output_file_path = os.path.join(temp_dir, f"{output_name}.md")

            with open(output_file_path, "w", encoding="utf-8") as f:
                f.write(processed_content)

            return (
                processed_content,
                output_file_path,
                "处理成功，已将无序列表转换为多级标题",
            )

        return processed_content, None, "处理成功，但无内容可供下载"

    except Exception as e:
        return "", None, f"处理Markdown时出错: {str(e)}"
