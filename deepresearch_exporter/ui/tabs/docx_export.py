"""
DOCX导出标签页
"""

import gradio as gr
from typing import Tuple

from ..handlers import process_file_with_docx, download_files


def create_docx_export_tab() -> gr.TabItem:
    """
    创建DOCX导出标签页

    Returns:
        DOCX导出标签页组件
    """
    with gr.TabItem("DOCX导出") as tab:
        gr.Markdown(
            "上传ChatGPT导出的Deep Research对话JSON文件，转换为Markdown、BibTeX和DOCX格式。"
        )

        with gr.Row():
            with gr.Column():
                docx_file_input = gr.File(
                    label="上传ChatGPT导出的JSON文件",
                    file_count="single",
                    file_types=[".json"],
                    type="filepath",
                )
                docx_output_name = gr.Textbox(label="输出文件名（不含扩展名，可选）")
                docx_mode = gr.Radio(
                    choices=["citations", "content_references"],
                    value="citations",
                    label="引用处理模式",
                )
                csl_file = gr.File(
                    label="CSL引用样式文件（可选）",
                    file_count="single",
                    file_types=[".csl"],
                    type="filepath",
                )
                ref_doc_file = gr.File(
                    label="DOCX参考文档（可选）",
                    file_count="single",
                    file_types=[".docx"],
                    type="filepath",
                )
                docx_process_btn = gr.Button("处理文件", variant="primary")
                docx_result_info = gr.Textbox(label="处理结果", interactive=False)

            with gr.Column():
                docx_md_output = gr.Textbox(label="Markdown内容", lines=8)
                docx_bib_output = gr.Textbox(label="BibTeX内容", lines=8)

                with gr.Row():
                    docx_md_download = gr.File(label="下载Markdown文件")
                    docx_bib_download = gr.File(label="下载BibTeX文件")
                    docx_download = gr.File(label="下载DOCX文件")

        # 处理逻辑
        docx_process_btn.click(
            fn=process_file_with_docx,
            inputs=[
                docx_file_input,
                docx_output_name,
                docx_mode,
                csl_file,
                ref_doc_file,
            ],
            outputs=[
                docx_md_output,
                docx_bib_output,
                docx_download,
                docx_result_info,
                gr.Textbox(visible=False),
            ],
        )

        # 连接处理结果和下载按钮
        docx_md_output.change(
            fn=download_files,
            inputs=[docx_md_output, docx_bib_output, docx_output_name],
            outputs=[docx_md_download, docx_bib_download],
        )

    return tab
