"""
Markdown标题处理标签页
"""

import gradio as gr

from ..markdown_handlers import process_markdown_headings, analyze_markdown_headings


def create_markdown_headings_tab() -> gr.TabItem:
    """
    创建Markdown标题处理标签页

    Returns:
        Markdown标题处理标签页组件
    """
    with gr.TabItem("Markdown标题处理") as tab:
        gr.Markdown(
            "调整Markdown文档中的标题层级，例如将所有标题升级或降级，或将一级标题作为文章标题。"
        )

        with gr.Tabs() as md_tabs:
            # 标题层级调整标签页
            with gr.TabItem("标题层级调整"):
                with gr.Row():
                    with gr.Column():
                        md_file_input = gr.File(
                            label="上传Markdown文件",
                            file_count="single",
                            file_types=[".md", ".markdown", ".txt"],
                            type="filepath",
                        )
                        md_output_name = gr.Textbox(
                            label="输出文件名（不含扩展名，可选）"
                        )

                        with gr.Row():
                            md_adjustment = gr.Slider(
                                label="标题级别调整",
                                minimum=-5,
                                maximum=5,
                                value=0,
                                step=1,
                                info="正数增加标题级别（如h1变为h2），负数减少标题级别（如h2变为h1）",
                            )

                        with gr.Row():
                            first_level_as_title = gr.Checkbox(
                                label="保留第一个一级标题作为文章标题", value=False
                            )

                        with gr.Accordion("高级选项", open=False):
                            with gr.Row():
                                min_level = gr.Slider(
                                    label="最小标题级别",
                                    minimum=1,
                                    maximum=6,
                                    value=1,
                                    step=1,
                                )
                                max_level = gr.Slider(
                                    label="最大标题级别",
                                    minimum=1,
                                    maximum=6,
                                    value=6,
                                    step=1,
                                )

                        md_process_btn = gr.Button("处理标题", variant="primary")
                        md_result_info = gr.Textbox(label="处理结果", interactive=False)

                    with gr.Column():
                        md_input_text = gr.Textbox(
                            label="Markdown内容",
                            lines=10,
                            placeholder="也可以直接粘贴Markdown内容到这里...",
                        )
                        md_output_text = gr.Textbox(label="处理后的内容", lines=10)
                        md_download_file = gr.File(label="下载处理后的文件")

            # 标题结构分析标签页
            with gr.TabItem("标题结构分析"):
                with gr.Row():
                    with gr.Column():
                        md_analyze_file = gr.File(
                            label="上传Markdown文件",
                            file_count="single",
                            file_types=[".md", ".markdown", ".txt"],
                            type="filepath",
                        )
                        md_analyze_btn = gr.Button("分析标题结构", variant="primary")
                        md_analyze_info = gr.Textbox(
                            label="分析结果", interactive=False
                        )

                    with gr.Column():
                        md_analyze_input = gr.Textbox(
                            label="Markdown内容",
                            lines=10,
                            placeholder="也可以直接粘贴Markdown内容到这里...",
                        )
                        md_analyze_output = gr.Textbox(label="分析报告", lines=15)

        # 标题处理逻辑
        md_process_btn.click(
            fn=process_markdown_headings,
            inputs=[
                md_file_input,
                md_input_text,
                md_adjustment,
                first_level_as_title,
                min_level,
                max_level,
                md_output_name,
            ],
            outputs=[md_output_text, md_download_file, md_result_info],
        )

        # 标题分析逻辑
        md_analyze_btn.click(
            fn=analyze_markdown_headings,
            inputs=[md_analyze_file, md_analyze_input],
            outputs=[md_analyze_output, md_analyze_info],
        )

    return tab
