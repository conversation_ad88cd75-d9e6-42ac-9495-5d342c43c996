"""
翻译部分（包含本地LLM翻译和在线翻译）
"""

import os
import gradio as gr
from ..handlers import handle_translate_content, handle_online_translate_content


def create_translation_section(editable_md_output):
    """创建翻译部分"""

    # 从环境变量中获取DeepL API密钥
    default_deepl_api_key = os.environ.get("DEEPL_API_KEY", "")

    with gr.Accordion("翻译当前Markdown", open=False):
        gr.Markdown("## 翻译选项")

        with gr.Tabs() as translation_tabs:
            with gr.TabItem("本地LLM翻译"):
                enable_translation_checkbox = gr.Checkbox(
                    label="启用本地LLM翻译", value=False
                )
                with gr.Row():
                    trans_source_lang_input = gr.Textbox(
                        label="源语言", value="English", interactive=False
                    )
                    trans_target_lang_input = gr.Textbox(
                        label="目标语言", value="Chinese", interactive=False
                    )
                trans_domain_input = gr.Textbox(
                    label="专业领域（可选）", interactive=False
                )
                with gr.Row():
                    trans_model_input = gr.Textbox(
                        label="模型名称", value="gemma3:4b", interactive=False
                    )
                    trans_base_url_input = gr.Textbox(
                        label="API地址",
                        value="http://localhost:11434/v1",
                        interactive=False,
                    )
                trans_api_key_input = gr.Textbox(
                    label="API密钥", value="ollama", type="password", interactive=False
                )
                with gr.Row():
                    trans_temperature_slider = gr.Slider(
                        label="温度",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.3,
                        step=0.1,
                        interactive=False,
                    )
                    trans_top_p_slider = gr.Slider(
                        label="Top-P",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.9,
                        step=0.1,
                        interactive=False,
                    )
                with gr.Row():
                    trans_batch_size_slider = gr.Slider(
                        label="批处理大小",
                        minimum=1,
                        maximum=20,
                        value=5,
                        step=1,
                        interactive=False,
                    )
                    trans_max_tokens_slider = gr.Slider(
                        label="最大Token数",
                        minimum=1000,
                        maximum=10000,
                        value=4000,
                        step=500,
                        interactive=False,
                    )
                trans_max_requests_slider = gr.Slider(
                    label="最大并发请求数",
                    minimum=1,
                    maximum=20,
                    value=5,
                    step=1,
                    interactive=False,
                )

                translation_params_inputs_list = [
                    trans_source_lang_input,
                    trans_target_lang_input,
                    trans_domain_input,
                    trans_model_input,
                    trans_base_url_input,
                    trans_api_key_input,
                    trans_temperature_slider,
                    trans_top_p_slider,
                    trans_batch_size_slider,
                    trans_max_tokens_slider,
                    trans_max_requests_slider,
                ]
                enable_translation_checkbox.change(
                    lambda x: [gr.update(interactive=x)]
                    * len(translation_params_inputs_list),
                    inputs=[enable_translation_checkbox],
                    outputs=translation_params_inputs_list,
                )

            with gr.TabItem("在线翻译"):
                gr.Markdown("""
                ### 在线翻译功能
                
                使用Google Translate或DeepL API翻译文本，无需本地LLM环境。
                
                - Google Translate：无需API密钥，但可能有流量限制
                - DeepL API：需要API密钥，但翻译质量更高
                  - 可在.env文件中设置DEEPL_API_KEY，将自动读取
                """)

                # 翻译设置
                with gr.Row():
                    online_translator_type = gr.Radio(
                        choices=["google", "deepl"],
                        value="google",
                        label="翻译服务",
                        info="Google Translate（无需API密钥）或DeepL API（需要API密钥）",
                    )

                    online_api_key = gr.Textbox(
                        label="API密钥",
                        placeholder="DeepL API密钥（使用Google Translate时可为空）",
                        type="password",
                        value=default_deepl_api_key,
                        visible=False,
                    )

                with gr.Row():
                    online_source_lang = gr.Dropdown(
                        label="源语言",
                        choices=[
                            "auto",
                            "en",
                            "zh-CN",
                            "ja",
                            "ko",
                            "fr",
                            "de",
                            "es",
                            "ru",
                            "pt",
                            "it",
                            "bg",
                            "cs",
                            "da",
                            "el",
                            "et",
                            "fi",
                            "hu",
                            "id",
                            "lt",
                            "lv",
                            "nb",
                            "nl",
                            "pl",
                            "ro",
                            "sk",
                            "sl",
                            "sv",
                            "tr",
                            "uk",
                        ],
                        value="auto",
                        info="自动检测或指定源语言",
                        allow_custom_value=True,
                    )
                    online_target_lang = gr.Dropdown(
                        label="目标语言",
                        choices=[
                            "zh-CN",
                            "en",
                            "en-US",
                            "en-GB",
                            "ja",
                            "ko",
                            "fr",
                            "de",
                            "es",
                            "ru",
                            "pt",
                            "pt-BR",
                            "pt-PT",
                            "it",
                            "bg",
                            "cs",
                            "da",
                            "el",
                            "et",
                            "fi",
                            "hu",
                            "id",
                            "lt",
                            "lv",
                            "nb",
                            "nl",
                            "pl",
                            "ro",
                            "sk",
                            "sl",
                            "sv",
                            "tr",
                            "uk",
                        ],
                        value="zh-CN",
                        info="目标语言",
                        allow_custom_value=True,
                    )

                online_formality = gr.Radio(
                    choices=["default", "more", "less"],
                    value="default",
                    label="语言风格（仅DeepL）",
                    info="default=标准，more=更正式，less=更随意",
                    visible=False,
                )

                with gr.Row(visible=False) as online_deepl_advanced_row:
                    online_preserve_formatting = gr.Checkbox(
                        value=True,
                        label="保留格式",
                        info="保留文本的原始格式，如标点和大小写",
                    )

                    online_split_sentences = gr.Dropdown(
                        choices=["0", "1", "nonewlines"],
                        value="1",
                        label="分句策略",
                        info="0=不分句，1=按标点和换行分句，nonewlines=仅按标点分句(忽略换行)。注意：使用quality_optimized模型时，此设置将被忽略。",
                    )

                with gr.Row(visible=False) as online_deepl_model_row:
                    online_tag_handling = gr.Dropdown(
                        choices=["", "xml", "html"],
                        value="",
                        label="标签处理",
                        info="处理文本中包含的XML或HTML标签",
                    )

                    online_model_type = gr.Dropdown(
                        choices=["", "quality_optimized", "prefer_quality_optimized"],
                        value="",
                        label="模型类型",
                        info="选择DeepL翻译模型类型",
                    )

                # 当翻译服务类型改变时，显示或隐藏相关选项
                def update_online_translator_options(choice):
                    if choice == "deepl":
                        return (
                            gr.update(visible=True),  # api_key
                            gr.update(visible=True),  # formality
                            gr.update(visible=True),  # deepl_advanced_row
                            gr.update(visible=True),  # deepl_model_row
                        )
                    else:
                        return (
                            gr.update(visible=False),  # api_key
                            gr.update(visible=False),  # formality
                            gr.update(visible=False),  # deepl_advanced_row
                            gr.update(visible=False),  # deepl_model_row
                        )

                online_translator_type.change(
                    fn=update_online_translator_options,
                    inputs=[online_translator_type],
                    outputs=[
                        online_api_key,
                        online_formality,
                        online_deepl_advanced_row,
                        online_deepl_model_row,
                    ],
                )

        # 添加临时编辑区
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 原始Markdown内容")
                with gr.Row():
                    trans_sync_from_main_btn = gr.Button(
                        "↓ 从主编辑器同步", variant="secondary"
                    )

            with gr.Column(scale=1):
                gr.Markdown("### 翻译预览")
                with gr.Row():
                    trans_apply_to_main_btn = gr.Button(
                        "↑ 应用到主编辑器", variant="primary"
                    )

        trans_input = gr.Textbox(
            label="用于翻译的Markdown内容",
            lines=10,
            interactive=True,
            placeholder="从主编辑器同步或粘贴Markdown内容...",
        )

        trans_preview = gr.Textbox(
            label="翻译预览结果",
            lines=10,
            interactive=False,
            placeholder="翻译预览将在此显示...",
        )

        with gr.Row():
            preview_local_trans_btn = gr.Button("预览本地LLM翻译", variant="secondary")
            preview_online_trans_btn = gr.Button("预览在线翻译", variant="secondary")

        # with gr.Row():
        #     execute_local_trans_btn = gr.Button("执行本地LLM翻译", variant="secondary")
        #     execute_online_trans_btn = gr.Button("执行在线翻译", variant="secondary")

        translation_log_output = gr.Textbox(
            label="翻译日志", lines=3, interactive=False
        )

    # 定义预览本地翻译效果的处理函数
    def handle_preview_local_translation(
        md_content,
        base_name_spec,
        json_file_obj,
        enabled,
        src_lang,
        tgt_lang,
        domain,
        model,
        base_url,
        api_key,
        temp,
        top_p,
        batch,
        max_tokens,
        max_req,
    ):
        if not enabled:
            return "翻译未启用。", md_content
        if not md_content:
            return "错误：Markdown内容为空，无法预览翻译效果。", md_content

        try:
            log_messages = []
            log_messages.append("开始预览本地LLM翻译效果...")

            # 使用同样的翻译逻辑，但输出到预览区域而不是主编辑器
            result, log = handle_translate_content(
                md_content,
                base_name_spec,
                json_file_obj,
                enabled,
                src_lang,
                tgt_lang,
                domain,
                model,
                base_url,
                api_key,
                temp,
                top_p,
                batch,
                max_tokens,
                max_req,
            )

            log_messages.append(log)

            return "\n".join(log_messages), result

        except Exception as e:
            return f"预览翻译失败: {str(e)}", md_content

    # 定义预览在线翻译效果的处理函数
    def handle_preview_online_translation(
        md_content,
        base_name_spec,
        json_file_obj,
        translator_type="google",
        api_key="",
        source_lang="auto",
        target_lang="zh-CN",
        formality="default",
        preserve_formatting=False,
        split_sentences="nonewlines",
        tag_handling="",
        model_type="",
    ):
        if not md_content:
            return "错误：Markdown内容为空，无法预览在线翻译效果。", md_content

        try:
            log_messages = []
            log_messages.append(f"开始预览在线翻译效果 (服务: {translator_type})...")

            # 确保所有参数都不为None
            translator_type = translator_type or "google"
            api_key = api_key or ""
            source_lang = source_lang or "auto"
            target_lang = target_lang or "zh-CN"
            formality = formality or "default"
            preserve_formatting = bool(preserve_formatting)
            split_sentences = split_sentences or "nonewlines"
            tag_handling = tag_handling or ""
            model_type = model_type or ""

            # 使用同样的翻译逻辑，但输出到预览区域而不是主编辑器
            result, log = handle_online_translate_content(
                md_content,
                base_name_spec,
                json_file_obj,
                translator_type,
                api_key,
                source_lang,
                target_lang,
                formality,
                preserve_formatting,
                split_sentences,
                tag_handling,
                model_type,
            )

            log_messages.append(log)

            return "\n".join(log_messages), result

        except Exception as e:
            return f"预览在线翻译失败: {str(e)}", md_content

    # 设置按钮点击事件
    trans_sync_from_main_btn.click(
        fn=lambda x: x, inputs=[editable_md_output], outputs=[trans_input]
    )

    preview_local_trans_btn.click(
        fn=handle_preview_local_translation,
        inputs=[
            trans_input,
            gr.Textbox(visible=False, value=""),  # 传递空字符串作为base_name_spec
            gr.State(None),  # 传递None作为json_file_obj
            enable_translation_checkbox,
            trans_source_lang_input,
            trans_target_lang_input,
            trans_domain_input,
            trans_model_input,
            trans_base_url_input,
            trans_api_key_input,
            trans_temperature_slider,
            trans_top_p_slider,
            trans_batch_size_slider,
            trans_max_tokens_slider,
            trans_max_requests_slider,
        ],
        outputs=[translation_log_output, trans_preview],
    )

    preview_online_trans_btn.click(
        fn=handle_preview_online_translation,
        inputs=[
            trans_input,
            gr.Textbox(visible=False, value=""),  # 传递空字符串作为base_name_spec
            gr.State(None),  # 传递None作为json_file_obj
            online_translator_type,
            online_api_key,
            online_source_lang,
            online_target_lang,
            online_formality,
            online_preserve_formatting,
            online_split_sentences,
            online_tag_handling,
            online_model_type,
        ],
        outputs=[translation_log_output, trans_preview],
    )

    trans_apply_to_main_btn.click(
        fn=lambda x: x,  # 简单传递函数
        inputs=[trans_preview],
        outputs=[editable_md_output],
    )

    # 返回主要组件以供引用
    return {
        # 本地LLM翻译选项
        "enable_translation_checkbox": enable_translation_checkbox,
        "trans_source_lang_input": trans_source_lang_input,
        "trans_target_lang_input": trans_target_lang_input,
        "trans_domain_input": trans_domain_input,
        "trans_model_input": trans_model_input,
        "trans_base_url_input": trans_base_url_input,
        "trans_api_key_input": trans_api_key_input,
        "trans_temperature_slider": trans_temperature_slider,
        "trans_top_p_slider": trans_top_p_slider,
        "trans_batch_size_slider": trans_batch_size_slider,
        "trans_max_tokens_slider": trans_max_tokens_slider,
        "trans_max_requests_slider": trans_max_requests_slider,
        # 在线翻译选项
        "online_translator_type": online_translator_type,
        "online_api_key": online_api_key,
        "online_source_lang": online_source_lang,
        "online_target_lang": online_target_lang,
        "online_formality": online_formality,
        "online_preserve_formatting": online_preserve_formatting,
        "online_split_sentences": online_split_sentences,
        "online_tag_handling": online_tag_handling,
        "online_model_type": online_model_type,
        # 共用组件
        "trans_input": trans_input,
        "trans_preview": trans_preview,
        "translation_log_output": translation_log_output,
    }
