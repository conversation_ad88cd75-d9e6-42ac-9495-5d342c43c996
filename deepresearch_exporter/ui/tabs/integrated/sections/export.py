"""
导出与保存部分
"""

import gradio as gr
from ..handlers import (
    handle_generate_docx,
    handle_save_md,
    handle_save_bib,
    handle_preview_citation,
)


def create_export_section(
    editable_md_output, editable_bib_output, output_basename_input, json_file_input
):
    """创建导出与保存部分"""

    with gr.Accordion("导出与保存", open=True):
        gr.Markdown("# 导出选项")

        # 添加引用整合选项
        with gr.Group():
            gr.Markdown("### 引用整合选项")
            apply_citation_integration = gr.Checkbox(
                label="导出前应用引用整合",
                value=False,
                info="启用后，导出前会先将引用标记[@citekey]替换为链接[n](url)",
            )

            with gr.Group(visible=False) as citation_options_group:
                citation_url_deduplicate = gr.Checkbox(
                    label="URL去重",
                    value=False,
                    info="如果多个引用指向同一URL，使用相同的编号，并在参考文献列表中只显示一个条目",
                )
                
                citation_complete_deduplicate = gr.Checkbox(
                    label="完全去重",
                    value=False,
                    info="启用后，相邻的相同URL引用将被合并为一个，避免连续出现相同标签",
                )

                citation_link_text_pattern = gr.Textbox(
                    label="链接文本模式",
                    value="[{index}]",
                    info="使用{index}作为序号占位符",
                )

                citation_append_references = gr.Checkbox(
                    label="添加参考文献列表",
                    value=False,
                    info="在文档末尾添加参考文献列表",
                )

        gr.Markdown("# 执行导出与下载")
        with gr.Row():
            generate_docx_btn = gr.Button("生成并下载DOCX", variant="secondary")
            save_md_btn = gr.Button("保存当前Markdown", variant="primary")
            save_bib_btn = gr.Button("保存当前BibTeX", variant="primary")

        with gr.Row():
            download_docx_file = gr.File(label="下载DOCX文件", interactive=False)
            download_md_file = gr.File(label="下载Markdown文件", interactive=False)
            download_bib_file = gr.File(label="下载BibTeX文件", interactive=False)

        export_log_output = gr.Textbox(label="导出日志", lines=3, interactive=False)

        # 创建一个隐藏的状态变量来存储处理后的临时内容，不影响原始输入
        processed_md_content = gr.State(None)

    # 处理引用整合选项组的显示/隐藏
    apply_citation_integration.change(
        fn=lambda x: gr.Group(visible=x),
        inputs=[apply_citation_integration],
        outputs=[citation_options_group],
    )

    # 处理函数: 根据是否启用引用整合处理Markdown内容，但不修改原始内容
    def process_md_with_citation_integration(
        md_content,
        bib_content,
        base_name_spec,
        json_file_obj,
        apply_integration,
        url_deduplicate,
        complete_deduplicate,
        link_text_pattern,
        append_references,
    ):
        if not apply_integration or not md_content or not bib_content:
            return None, "未应用引用整合。"

        # 使用引用整合处理
        log, processed_content = handle_preview_citation(
            md_content,
            bib_content,
            base_name_spec,
            json_file_obj,
            url_deduplicate,
            complete_deduplicate,
            link_text_pattern,
            append_references,
        )

        # 返回处理后的内容作为临时变量，而不是直接修改输入框
        return processed_content, f"应用引用整合:\n{log}"

    # 修改后的生成DOCX处理函数，使用临时处理内容
    def handle_generate_docx_with_citation(
        md_content, temp_processed_md, bib_content, base_name_spec, json_file_obj
    ):
        # 如果有临时处理的内容，使用它；否则使用原始内容
        content_to_use = (
            temp_processed_md if temp_processed_md is not None else md_content
        )
        return handle_generate_docx(
            content_to_use, bib_content, base_name_spec, json_file_obj
        )

    # 修改后的保存MD处理函数，使用临时处理内容
    def handle_save_md_with_citation(
        md_content, temp_processed_md, base_name_spec, json_file_obj
    ):
        # 如果有临时处理的内容，使用它；否则使用原始内容
        content_to_use = (
            temp_processed_md if temp_processed_md is not None else md_content
        )
        return handle_save_md(content_to_use, base_name_spec, json_file_obj)

    # 设置按钮点击事件
    generate_docx_btn.click(
        fn=process_md_with_citation_integration,
        inputs=[
            editable_md_output,
            editable_bib_output,
            output_basename_input,
            json_file_input,
            apply_citation_integration,
            citation_url_deduplicate,
            citation_complete_deduplicate,
            citation_link_text_pattern,
            citation_append_references,
        ],
        outputs=[processed_md_content, export_log_output],  # 更新临时处理内容和日志
    ).then(  # 然后继续处理DOCX生成
        fn=handle_generate_docx_with_citation,
        inputs=[
            editable_md_output,  # 原始内容
            processed_md_content,  # 临时处理的内容
            editable_bib_output,
            output_basename_input,
            json_file_input,
        ],
        outputs=[download_docx_file, export_log_output],
    )

    save_md_btn.click(
        fn=process_md_with_citation_integration,
        inputs=[
            editable_md_output,
            editable_bib_output,
            output_basename_input,
            json_file_input,
            apply_citation_integration,
            citation_url_deduplicate,
            citation_complete_deduplicate,
            citation_link_text_pattern,
            citation_append_references,
        ],
        outputs=[processed_md_content, export_log_output],  # 更新临时处理内容和日志
    ).then(  # 然后继续处理保存MD
        fn=handle_save_md_with_citation,
        inputs=[
            editable_md_output,  # 原始内容
            processed_md_content,  # 临时处理的内容
            output_basename_input,
            json_file_input,
        ],
        outputs=[download_md_file, export_log_output],
    )

    save_bib_btn.click(
        fn=handle_save_bib,
        inputs=[editable_bib_output, output_basename_input, json_file_input],
        outputs=[download_bib_file, export_log_output],
    )

    # 返回主要组件以供引用
    return {
        "download_docx_file": download_docx_file,
        "download_md_file": download_md_file,
        "download_bib_file": download_bib_file,
        "export_log_output": export_log_output,
        "apply_citation_integration": apply_citation_integration,
        "citation_url_deduplicate": citation_url_deduplicate,
        "citation_complete_deduplicate": citation_complete_deduplicate,
        "citation_link_text_pattern": citation_link_text_pattern,
        "citation_append_references": citation_append_references,
    }
