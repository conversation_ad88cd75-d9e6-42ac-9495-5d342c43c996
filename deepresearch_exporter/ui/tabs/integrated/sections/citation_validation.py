"""
引用验证部分
"""

import re
import os
import gradio as gr
import pandas as pd
import tempfile

from .....parser.citation_validator import (
    validate_markdown_citations_without_highlight,
)
from .....logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def create_citation_validation_section(editable_md_output, editable_bib_output):
    """创建引用验证部分"""

    with gr.Accordion("引用验证", open=False):
        gr.Markdown("""
        ### 引用验证与替换工具
        
        检测 Markdown 文档中缺失的引用标记，提供替换选项。
        """)

        # 状态显示
        validation_status = gr.Markdown(label="状态")

        # 操作按钮
        with gr.Row():
            check_button = gr.Button("检查缺失引用", variant="primary")

        # 缺失引用编辑区域
        missing_citations_area = gr.Row(visible=False)
        with missing_citations_area:
            with gr.Column():
                gr.Markdown("### 缺失的引用及替换建议")
                missing_citations_table = gr.Dataframe(
                    headers=["缺失的引用键", "替换为"],
                    datatype=["str", "str"],
                    label="编辑下方的'替换为'列以修改替换值",
                    interactive=True,
                    row_count=10,
                    col_count=(2, "fixed"),
                )
                apply_button = gr.Button("应用替换", variant="primary")

        # 状态存储
        state = gr.State({})

        # 结果预览区域
        result_area = gr.Row(visible=False)
        with result_area:
            markdown_preview = gr.Textbox(label="替换预览", lines=10, interactive=False)
            apply_to_main_btn = gr.Button("↑ 应用到主编辑器", variant="primary")

        validation_log = gr.Textbox(label="验证日志", lines=3, interactive=False)

        # 处理函数
        def check_citations_from_text(markdown_content, bibtex_content):
            if not markdown_content or not bibtex_content:
                return (
                    "请确保主编辑器中有 Markdown 内容和 BibTeX 内容",
                    {},
                    None,
                    gr.Row(visible=False),
                )

            try:
                # 创建临时文件存储内容
                with tempfile.NamedTemporaryFile(
                    "w", encoding="utf-8", suffix=".md", delete=False
                ) as md_file:
                    md_file.write(markdown_content)
                    md_path = md_file.name

                with tempfile.NamedTemporaryFile(
                    "w", encoding="utf-8", suffix=".bib", delete=False
                ) as bib_file:
                    bib_file.write(bibtex_content)
                    bib_path = bib_file.name

                # 检测缺失引用
                is_all_valid, missing_keys, suggestions = (
                    validate_markdown_citations_without_highlight(md_path, bib_path)
                )

                # 清理临时文件
                os.unlink(md_path)
                os.unlink(bib_path)

                if not missing_keys:
                    return (
                        "✅ 没有发现缺失的引用！文档中的所有引用都在BibTeX文件中有对应条目。",
                        {},
                        None,
                        gr.Row(visible=False),
                    )

                # 生成状态信息
                state_info = {
                    "markdown_content": markdown_content,
                    "missing_keys": missing_keys,
                    "suggestions": suggestions,
                }

                # 准备表格数据
                table_data = []
                for key in missing_keys:
                    suggestion_list = suggestions.get(key, [])
                    suggested_key = suggestion_list[0] if suggestion_list else ""
                    table_data.append([key, suggested_key])

                # 生成报告
                keys_count = len(missing_keys)
                report = f"❌ 发现 {keys_count} 个缺失的引用，请在下面编辑替换值"

                return (
                    report,
                    state_info,
                    pd.DataFrame(table_data, columns=["缺失的引用键", "替换为"]),
                    gr.Row(visible=True),
                )

            except Exception as e:
                logger.error(f"检查引用时出错: {str(e)}")
                return f"处理失败: {str(e)}", {}, None, gr.Row(visible=False)

        def apply_replacements_to_text(table_data, state_info):
            if not state_info or not state_info.get("missing_keys"):
                return "请先检查缺失引用", "", gr.Row(visible=False)

            if table_data is None or len(table_data) == 0:
                return "没有找到缺失引用数据", "", gr.Row(visible=False)

            try:
                markdown_content = state_info["markdown_content"]

                # 创建替换表
                replacements = {}
                for _, row in table_data.iterrows():
                    if len(row) >= 2:
                        old_key = row[0]
                        new_key = row[1]
                        if new_key:  # 只替换有值的项
                            replacements[old_key] = new_key

                if not replacements:
                    return "没有提供有效的替换值", "", gr.Row(visible=False)

                # 应用替换
                for old_key, new_key in replacements.items():
                    if not new_key:  # 如果替换值为空，则跳过
                        continue

                    # 替换[@old_key]格式的引用
                    pattern = r"\[@" + re.escape(old_key) + r"\]"
                    markdown_content = re.sub(
                        pattern, f"[@{new_key}]", markdown_content
                    )

                # 计算替换项数量
                replaced_count = len(replacements)
                result_msg = f"✅ 已成功替换 {replaced_count} 项引用"

                return result_msg, markdown_content, gr.Row(visible=True)

            except Exception as e:
                logger.error(f"应用替换时出错: {str(e)}")
                return f"替换失败: {str(e)}", "", gr.Row(visible=False)

        # 连接函数
        check_button.click(
            check_citations_from_text,
            inputs=[editable_md_output, editable_bib_output],
            outputs=[
                validation_status,
                state,
                missing_citations_table,
                missing_citations_area,
            ],
        )

        apply_button.click(
            apply_replacements_to_text,
            inputs=[missing_citations_table, state],
            outputs=[validation_status, markdown_preview, result_area],
        )

        apply_to_main_btn.click(
            fn=lambda x: x,  # 简单传递函数
            inputs=[markdown_preview],
            outputs=[editable_md_output],
        )

        # 使用说明
        with gr.Accordion("使用说明", open=False):
            gr.Markdown("""
            ### 使用方法
            
            1. 确保主编辑器中已有 Markdown 内容和 BibTeX 内容
            2. 点击"检查缺失引用"按钮
            3. 如果有缺失的引用，在表格中编辑"替换为"列的值
               - 系统会自动填入推荐的替换值（如有）
               - 您可以根据需要修改替换值
               - 如果不需要替换某项，可以将其替换值置空
            4. 点击"应用替换"按钮执行替换操作
            5. 查看替换预览并点击"应用到主编辑器"确认修改
            
            ### 示例
            
            Markdown 文件中有引用 `[@missing2023]`，但 BibTeX 文件中没有对应条目。
            系统可能会推荐替换为 `[@smith2023]`，您可以接受这个建议或输入其他值。
            应用替换后，文档中的 `[@missing2023]` 将被替换为您指定的引用键。
            """)

    # 返回主要组件以供引用
    return {
        "validation_status": validation_status,
        "missing_citations_table": missing_citations_table,
        "markdown_preview": markdown_preview,
        "validation_log": validation_log,
    }
