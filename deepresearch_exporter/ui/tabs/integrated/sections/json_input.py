"""
JSON输入与初始解析部分
"""

import gradio as gr
from ..handlers import handle_parse_json


def create_json_input_section():
    """创建JSON输入与初始解析部分"""

    with gr.Accordion("JSON 输入与初始解析", open=True):
        with gr.Row():
            with gr.<PERSON>umn(scale=1):
                gr.Markdown("# 输入文件和基础设置")
                json_file_input = gr.File(
                    label="上传ChatGPT导出的JSON文件",
                    file_count="single",
                    file_types=[".json"],
                    type="filepath",  # Gradio File component returns a tempfile._TemporaryFileWrapper object
                )
                output_basename_input = gr.Textbox(
                    label="输出文件名前缀（可选，例如：'my_research'）"
                )
                citation_mode_input = gr.Radio(
                    choices=["citations", "content_references"],
                    value="citations",
                    label="引用处理模式",
                )
                parse_json_btn = gr.<PERSON><PERSON>("1. 解析JSON文件", variant="primary")

            with gr.<PERSON>umn(scale=2):
                gr.Markdown("# 解析结果")
                editable_md_output = gr.Textbox(
                    label="Markdown 内容 (可编辑)",
                    lines=15,
                    interactive=True,
                    placeholder="JSON解析后的Markdown内容将在此显示...",
                )
                editable_bib_output = gr.Textbox(
                    label="BibTeX 内容 (可编辑)",
                    lines=10,
                    interactive=True,
                    placeholder="JSON解析后的BibTeX内容将在此显示...",
                )
                parse_log_output = gr.Textbox(
                    label="解析日志", lines=3, interactive=False
                )

    # 设置按钮点击事件
    parse_json_btn.click(
        fn=handle_parse_json,
        inputs=[json_file_input, output_basename_input, citation_mode_input],
        outputs=[editable_md_output, editable_bib_output, parse_log_output],
    )

    return (
        json_file_input,
        output_basename_input,
        citation_mode_input,
        editable_md_output,
        editable_bib_output,
        parse_log_output,
        parse_json_btn,
    )
