"""
Markdown内容后处理部分
"""

import gradio as gr
from ..handlers import handle_md_processing


def create_markdown_processing_section(editable_md_output):
    """创建Markdown内容后处理部分"""

    with gr.Accordion("Markdown 内容后处理", open=False):
        gr.Markdown("Markdown后处理选项")

        with gr.Row():
            process_headings_checkbox = gr.Checkbox(
                label="处理标题层级 (调整等操作)", value=True
            )
            process_lists_checkbox = gr.Checkbox(
                label="简单列表转为Markdown标题 (默认使用##)", value=True
            )

        with gr.Accordion("标题处理高级选项", open=False):
            with gr.Row():
                md_adjustment = gr.Slider(
                    label="标题级别调整",
                    minimum=-5,
                    maximum=5,
                    value=0,
                    step=1,
                    info="正数增加标题级别（如h1变为h2），负数减少标题级别（如h2变为h1）",
                )
            with gr.Row():
                first_level_as_title = gr.Checkbox(
                    label="保留第一个一级标题作为文章标题", value=False
                )
            with gr.Row():
                min_level = gr.Slider(
                    label="最小标题级别", minimum=1, maximum=6, value=1, step=1
                )
                max_level = gr.Slider(
                    label="最大标题级别", minimum=1, maximum=6, value=6, step=1
                )

        # 添加临时编辑区
        with gr.Row():
            with gr.Column(scale=1):
                gr.Markdown("### 原始Markdown内容")
                with gr.Row():
                    md_processing_sync_from_main_btn = gr.Button(
                        "↓ 从主编辑器同步", variant="secondary"
                    )

            with gr.Column(scale=1):
                gr.Markdown("### 处理预览")
                with gr.Row():
                    md_processing_apply_to_main_btn = gr.Button(
                        "↑ 应用到主编辑器", variant="primary"
                    )

        md_processing_input = gr.Textbox(
            label="用于处理的Markdown内容",
            lines=10,
            interactive=True,
            placeholder="从主编辑器同步或粘贴Markdown内容...",
        )

        md_processing_preview = gr.Textbox(
            label="处理预览结果",
            lines=10,
            interactive=False,
            placeholder="处理预览将在此显示...",
        )

        with gr.Row():
            preview_md_processing_btn = gr.Button("预览处理效果", variant="secondary")
            apply_md_processing_btn = gr.Button(
                "2. 应用Markdown后处理", variant="secondary"
            )

        md_processing_log_output = gr.Textbox(
            label="Markdown处理日志", lines=3, interactive=False
        )

    # 定义预览Markdown处理效果的处理函数
    def handle_preview_md_processing(
        md_content,
        base_name_spec,
        json_file_obj,
        apply_headings,
        apply_lists,
        md_adjustment,
        first_level_as_title,
        min_level,
        max_level,
    ):
        if not md_content:
            return "错误：Markdown内容为空，无法预览处理效果。", md_content

        try:
            log_messages = []
            log_messages.append("开始预览Markdown处理效果...")

            # 使用同样的处理逻辑，但输出到预览区域而不是主编辑器
            result, log = handle_md_processing(
                md_content,
                base_name_spec,
                json_file_obj,
                apply_headings,
                apply_lists,
                md_adjustment,
                first_level_as_title,
                min_level,
                max_level,
            )

            log_messages.append(log)

            return "\n".join(log_messages), result

        except Exception as e:
            return f"预览处理失败: {str(e)}", md_content

    # 设置按钮点击事件
    md_processing_sync_from_main_btn.click(
        fn=lambda x: x, inputs=[editable_md_output], outputs=[md_processing_input]
    )

    preview_md_processing_btn.click(
        fn=handle_preview_md_processing,
        inputs=[
            md_processing_input,
            gr.Textbox(visible=False, value=""),  # 传递空字符串作为base_name_spec
            gr.State(None),  # 传递None作为json_file_obj
            process_headings_checkbox,
            process_lists_checkbox,
            md_adjustment,
            first_level_as_title,
            min_level,
            max_level,
        ],
        outputs=[md_processing_log_output, md_processing_preview],
    )

    md_processing_apply_to_main_btn.click(
        fn=lambda x: x,  # 简单传递函数
        inputs=[md_processing_preview],
        outputs=[editable_md_output],
    )

    apply_md_processing_btn.click(
        fn=handle_md_processing,
        inputs=[
            editable_md_output,
            gr.Textbox(visible=False, value=""),  # 传递空字符串作为base_name_spec
            gr.State(None),  # 传递None作为json_file_obj
            process_headings_checkbox,
            process_lists_checkbox,
            md_adjustment,
            first_level_as_title,
            min_level,
            max_level,
        ],
        outputs=[editable_md_output, md_processing_log_output],
    )

    # 返回主要组件以供引用
    return {
        "process_headings_checkbox": process_headings_checkbox,
        "process_lists_checkbox": process_lists_checkbox,
        "md_adjustment": md_adjustment,
        "first_level_as_title": first_level_as_title,
        "min_level": min_level,
        "max_level": max_level,
        "md_processing_input": md_processing_input,
        "md_processing_preview": md_processing_preview,
        "md_processing_log_output": md_processing_log_output,
    }
