"""
整合版功能标签页 - 单页流程
"""

import gradio as gr
from .sections.json_input import create_json_input_section
from .sections.markdown_processing import create_markdown_processing_section
from .sections.translation import create_translation_section
from .sections.citation_validation import create_citation_validation_section
from .sections.export import create_export_section
import os


def create_integrated_tab() -> gr.TabItem:
    """
    创建流水线式整合功能标签页
    """
    with gr.TabItem("All in One") as tab:
        gr.Markdown("# Deep Research All-in-One")
        gr.Markdown("分步处理您的JSON文件，实时预览和编辑，灵活导出。")

        # 创建各个部分
        (
            json_file_input,
            output_basename_input,
            citation_mode_input,
            editable_md_output,
            editable_bib_output,
            parse_log_output,
            parse_json_btn,
        ) = create_json_input_section()

        md_processing = create_markdown_processing_section(editable_md_output)

        translation = create_translation_section(editable_md_output)

        citation_validation = create_citation_validation_section(
            editable_md_output, editable_bib_output
        )

        export_section = create_export_section(
            editable_md_output,
            editable_bib_output,
            output_basename_input,
            json_file_input,
        )

        # --- 配置共享状态和回调 ---

        # 从基本配置获取文件基本名称的辅助函数
        def get_base_name(json_file, specified_base_name):
            if specified_base_name:
                return specified_base_name
            if json_file and hasattr(json_file, "name"):
                return os.path.splitext(os.path.basename(json_file.name))[0]
            return "default_output"

    return tab
