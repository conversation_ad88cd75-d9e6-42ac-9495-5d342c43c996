"""
集成标签页的处理函数
"""

import os
import tempfile
from pathlib import Path
from deepresearch_exporter.ui.handlers import (
    process_file,
    convert_to_docx as handler_convert_to_docx,
    translate_file_content as handler_translate_file_content,
    online_translate_file_content as handler_online_translate_file_content,
)
from deepresearch_exporter.ui.markdown_handlers import (
    process_markdown_headings as handler_process_markdown_headings,
    process_lists_to_headings as handler_process_lists_to_headings,
)
from ....tools.bibtex import BibtexProcessor
from ....core.validator import CitationValidator


# 辅助函数: 获取基础文件名
def get_base_name(json_file, specified_base_name):
    if specified_base_name:
        return specified_base_name
    if json_file and hasattr(json_file, "name"):
        return os.path.splitext(os.path.basename(json_file.name))[0]
    return "default_output"


# 辅助函数: 保存内容到临时文件
def save_to_temp_file(content, suffix, base_name_spec, json_file_obj):
    _base_name = get_base_name(json_file_obj, base_name_spec)
    try:
        with tempfile.NamedTemporaryFile(
            mode="w",
            delete=False,
            suffix=suffix,
            encoding="utf-8",
            prefix=f"{_base_name}_",
        ) as tmp_file:
            tmp_file.write(content)
            # 确保返回字符串类型的路径
            return str(tmp_file.name)
    except Exception as e:
        # print(f"Error saving to temp file: {e}") # For debugging
        return None


# 处理函数: 解析JSON
def handle_parse_json(json_file, base_name_spec, mode):
    if not json_file:
        return "", "", "错误：请先上传JSON文件。"

    _base_name = get_base_name(json_file, base_name_spec)

    try:
        # process_file expects a file path for json_file
        # The Gradio File component provides a tempfile._TemporaryFileWrapper object which has a .name attribute (path)
        md_content, bib_content, process_msg = process_file(
            json_file.name, _base_name, mode
        )

        if "错误" in process_msg:
            return md_content or "", bib_content or "", process_msg
        return md_content, bib_content, process_msg
    except Exception as e:
        return "", "", f"处理JSON时发生意外错误: {str(e)}"


# 处理函数: Markdown处理
def handle_md_processing(
    current_md_content,
    base_name_spec,
    json_file_obj,
    apply_headings,
    apply_lists,
    md_adjustment,
    first_level_as_title,
    min_level,
    max_level,
):
    if not current_md_content:
        return current_md_content, "错误：Markdown内容为空，无法处理。"

    _base_name = get_base_name(
        json_file_obj, base_name_spec
    )  # Use original JSON for consistent base name if not specified
    log_messages = []
    processed_md_content = current_md_content

    temp_dir_md_process = (
        tempfile.mkdtemp()
    )  # Create a temp dir for intermediate files if needed

    if apply_headings:
        try:
            # handler_process_markdown_headings can take markdown_text directly
            md_content_after_headings, temp_headings_path, msg = (
                handler_process_markdown_headings(
                    None,  # markdown_file (path) is None
                    processed_md_content,  # markdown_text
                    md_adjustment,
                    first_level_as_title,
                    min_level,
                    max_level,  # use the user-specified values
                    output_name=os.path.join(
                        temp_dir_md_process, f"{_base_name}_headings_processed"
                    ),
                )
            )
            log_messages.append(f"标题处理: {msg}")
            if "错误" not in msg:
                processed_md_content = md_content_after_headings
            # temp_headings_path is a temp file, content is in md_content_after_headings
        except Exception as e:
            log_messages.append(f"标题处理失败: {str(e)}")

    if apply_lists:
        try:
            # handler_process_lists_to_headings can take markdown_text directly
            md_content_after_lists, temp_lists_path, msg = (
                handler_process_lists_to_headings(
                    None,  # markdown_file (path) is None
                    processed_md_content,  # markdown_text (which might be after heading processing)
                    output_name=os.path.join(
                        temp_dir_md_process, f"{_base_name}_lists_processed"
                    ),
                )
            )
            log_messages.append(f"列表转标题处理: {msg}")
            if "错误" not in msg:
                processed_md_content = md_content_after_lists
            # temp_lists_path is a temp file, content is in md_content_after_lists
        except Exception as e:
            log_messages.append(f"列表转标题处理失败: {str(e)}")

    # Clean up temp_dir_md_process if it was used and files were created
    # For simplicity, Gradio handles file paths from handlers if they are returned for download.
    # Here, we only return the string content and logs.

    return processed_md_content, "\n".join(
        log_messages
    ) if log_messages else "未进行任何处理或无消息。"


# 处理函数: 翻译
def handle_translate_content(
    current_md_content,
    base_name_spec,
    json_file_obj,
    enabled,
    src_lang,
    tgt_lang,
    domain,
    model,
    base_url,
    api_key,
    temp,
    top_p,
    batch,
    max_tokens,
    max_req,
):
    if not enabled:
        return current_md_content, "翻译未启用。"
    if not current_md_content:
        return current_md_content, "错误：Markdown内容为空，无法翻译。"

    _base_name = get_base_name(json_file_obj, base_name_spec)

    try:
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".md", encoding="utf-8"
        ) as tmp_md_file:
            tmp_md_file.write(current_md_content)
            md_file_path = tmp_md_file.name

        output_translated_name = f"{_base_name}_translated"

        orig_content, translated_content, translated_file_path, msg = (
            handler_translate_file_content(
                md_file_path,
                base_url,
                api_key,
                model,
                src_lang,
                tgt_lang,
                domain,
                temp,
                top_p,
                batch,
                max_tokens,
                max_req,
                output_name=output_translated_name,  # handler will create file in its default temp location or specified
            )
        )

        os.unlink(md_file_path)  # Clean up the temp input file

        if "错误" not in msg and translated_content:
            # The handler_translate_file_content saves the file and returns path.
            # We should read content from translated_content for the textbox.
            return (
                translated_content,
                f"翻译成功: {msg}. 输出文件: {translated_file_path if translated_file_path else 'N/A'}",
            )
        else:
            return (
                current_md_content,
                f"翻译失败: {msg}",
            )  # Return original content on failure
    except Exception as e:
        return current_md_content, f"翻译时发生意外错误: {str(e)}"


# 处理函数: 在线翻译
def handle_online_translate_content(
    current_md_content,
    base_name_spec,
    json_file_obj,
    translator_type="google",
    api_key="",
    source_lang="auto",
    target_lang="zh-CN",
    formality="default",
    preserve_formatting=False,
    split_sentences="nonewlines",
    tag_handling="",
    model_type="",
):
    if not current_md_content:
        return current_md_content, "错误：Markdown内容为空，无法翻译。"

    # 确保所有参数都不为None
    translator_type = translator_type or "google"
    api_key = api_key or ""
    source_lang = source_lang or "auto"
    target_lang = target_lang or "zh-CN"
    formality = formality or "default"
    preserve_formatting = bool(preserve_formatting)
    split_sentences = split_sentences or "nonewlines"
    tag_handling = tag_handling if tag_handling else ""
    model_type = model_type if model_type else ""

    _base_name = get_base_name(json_file_obj, base_name_spec)

    try:
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".md", encoding="utf-8"
        ) as tmp_md_file:
            tmp_md_file.write(current_md_content)
            md_file_path = tmp_md_file.name

        output_translated_name = f"{_base_name}_translated"

        orig_content, translated_content, translated_file_path, msg = (
            handler_online_translate_file_content(
                md_file_path,
                translator_type,
                api_key,
                source_lang,
                target_lang,
                formality,
                preserve_formatting,
                split_sentences,
                tag_handling,
                model_type,
                output_name=output_translated_name,
            )
        )

        os.unlink(md_file_path)  # Clean up the temp input file

        if "错误" not in msg and translated_content:
            # 处理成功，返回翻译后的内容
            return (
                translated_content,
                f"在线翻译成功: {msg}. 输出文件: {translated_file_path if translated_file_path else 'N/A'}",
            )
        else:
            return (
                current_md_content,
                f"在线翻译失败: {msg}",
            )  # Return original content on failure
    except Exception as e:
        return current_md_content, f"在线翻译过程中发生意外错误: {str(e)}"


# 处理函数: 引用整合预览
def handle_preview_citation(
    md_content,
    bib_content,
    base_name_spec,
    json_file_obj,
    url_deduplicate,
    complete_deduplicate,
    link_text_pattern,
    append_references,
):
    if not md_content or not bib_content:
        return "错误：Markdown或BibTeX内容为空，无法预览。", md_content

    try:
        log_messages = []
        log_messages.append("开始预览引用整合效果...")

        _base_name = get_base_name(json_file_obj, base_name_spec)

        # 将当前内容保存到临时文件
        temp_md_path = save_to_temp_file(
            md_content, ".md", base_name_spec, json_file_obj
        )
        temp_bib_path = save_to_temp_file(
            bib_content, ".bib", base_name_spec, json_file_obj
        )

        if not temp_md_path or not temp_bib_path:
            return "错误：无法创建临时文件。", md_content

        # 创建临时目录用于输出
        tmp_dir = Path(tempfile.mkdtemp())
        output_filename = f"{_base_name}_citation_preview.md"
        output_path = tmp_dir / output_filename

        # 处理引用替换
        log_messages.append("生成引用整合预览...")
        bibtex_processor = BibtexProcessor()
        result_path = bibtex_processor.merge_markdown_with_bibtex(
            temp_md_path,
            temp_bib_path,
            str(output_path),
            url_deduplicate=url_deduplicate,
            complete_deduplicate=complete_deduplicate,
            link_text_pattern=link_text_pattern,
            append_references=append_references,
        )

        # 读取处理结果
        with open(result_path, "r", encoding="utf-8") as f:
            processed_content = f.read()

        # 清理临时文件
        if os.path.exists(temp_md_path):
            os.unlink(temp_md_path)
        if os.path.exists(temp_bib_path):
            os.unlink(temp_bib_path)

        log_messages.append("引用整合预览生成完成!")
        if append_references:
            log_messages.append("预览中包含参考文献列表")

        if url_deduplicate:
            log_messages.append(
                "URL去重模式已启用: 相同URL的引用将共享同一个序号，参考文献列表中每个序号只显示一个条目"
            )
            
        if complete_deduplicate:
            log_messages.append(
                "完全去重模式已启用: 相邻的相同URL引用将被合并为一个，避免连续出现相同标签"
            )

        return "\n".join(log_messages), processed_content

    except Exception as e:
        return f"引用整合预览失败: {str(e)}", md_content


# 处理函数: BibTeX预览
def handle_preview_bibtex(current_bib_content, base_name_spec, json_file_obj):
    if not current_bib_content:
        return "错误：BibTeX内容为空，无法预览。", {}

    try:
        log_messages = []
        log_messages.append("开始分析BibTeX内容...")

        # 将当前BibTeX内容保存到临时文件
        temp_bib_path = save_to_temp_file(
            current_bib_content, ".bib", base_name_spec, json_file_obj
        )

        if not temp_bib_path:
            return "错误：无法创建临时BibTeX文件。", {}

        # 加载BibTeX文件
        bibtex_processor = BibtexProcessor()
        citations = bibtex_processor.load_bibtex_entries(temp_bib_path)
        log_messages.append(f"成功读取 {len(citations)} 个BibTeX条目")

        # 准备用于展示的数据
        entries_data = []
        for key, entry in citations.items():
            authors_str = ", ".join(entry.get("authors", []))[:50]
            if len(authors_str) > 50:
                authors_str += "..."

            title = entry.get("title", "")[:50]
            if len(title) > 50:
                title += "..."

            entries_data.append(
                [
                    key,
                    entry.get("type", ""),
                    title,
                    authors_str,
                    entry.get("year", ""),
                    entry.get("url", "")[:50],
                    entry.get("doi", ""),
                    entry.get("journal", "")[:50],
                ]
            )

        # 清理临时文件
        if os.path.exists(temp_bib_path):
            os.unlink(temp_bib_path)

        log_messages.append("BibTeX条目加载完成，请查看下方表格")

        return "\n".join(log_messages), entries_data

    except Exception as e:
        return f"处理失败：{str(e)}", {}


# 处理函数: 引用验证
def handle_validate_citations(md_content, bib_content, base_name_spec, json_file_obj):
    if not md_content or not bib_content:
        return (
            "错误：Markdown或BibTeX内容为空，无法验证。",
            "",
            "验证失败：缺少必要内容",
            {},
            [],
        )

    try:
        log_messages = []
        log_messages.append("开始验证引用...")

        _base_name = get_base_name(json_file_obj, base_name_spec)

        # 将当前内容保存到临时文件
        temp_md_path = save_to_temp_file(
            md_content, ".md", base_name_spec, json_file_obj
        )
        temp_bib_path = save_to_temp_file(
            bib_content, ".bib", base_name_spec, json_file_obj
        )

        if not temp_md_path or not temp_bib_path:
            return "错误：无法创建临时文件。", "", "验证失败：无法创建临时文件", {}, []

        # 确保路径是字符串类型
        if isinstance(temp_md_path, Path):
            temp_md_path = str(temp_md_path)
        if isinstance(temp_bib_path, Path):
            temp_bib_path = str(temp_bib_path)

        # 创建临时目录用于输出高亮文件
        tmp_dir = Path(tempfile.mkdtemp())
        output_filename = f"{_base_name}_validated.md"
        output_path = tmp_dir / output_filename
        output_path_str = str(output_path)

        # 验证引用
        validator = CitationValidator()
        is_all_valid, missing_keys, suggestions = validator.validate_citations(temp_md_path, temp_bib_path)

        # 生成高亮文件
        highlighted_path = str(tmp_dir / f"{_base_name}_highlighted.md")
        validator.highlight_missing_citations(temp_md_path, temp_bib_path, highlighted_path)

        # 生成报告
        report = validator.generate_report(missing_keys, suggestions)

        # 读取高亮后的 Markdown 文本用于预览
        with open(highlighted_path, "r", encoding="utf-8") as f:
            highlighted_text = f.read()

        # 返回的状态信息，包含原始文件路径和建议
        state = {
            "markdown_path": temp_md_path,
            "bibtex_path": temp_bib_path,
            "missing_keys": missing_keys,
            "suggestions": suggestions,
        }

        # 准备数据表格的数据
        table_data = []
        for key in missing_keys:
            suggestion_list = suggestions.get(key, [])
            suggested_key = suggestion_list[0] if suggestion_list else ""
            table_data.append([key, suggested_key, True if suggested_key else False])

        log_messages.append(f"验证完成！找到 {len(missing_keys)} 个缺失的引用键。")

        return report, highlighted_text, "\n".join(log_messages), state, table_data

    except Exception as e:
        return f"验证失败: {str(e)}", "", f"验证过程中出错: {str(e)}", {}, []


# 处理函数: 替换引用
def replace_single_citation(missing_cite, suggested_cite, custom_cite, state):
    if not missing_cite:
        return "请先选择需要替换的引用", None

    if not suggested_cite and not custom_cite:
        return "请选择或输入替换的引用键", None

    try:
        # 获取替换键
        old_key = missing_cite.strip("[@]")

        if custom_cite:
            new_key = custom_cite.strip("[@]")
        else:
            new_key = suggested_cite.strip("[@]")

        # 创建临时目录
        tmp_dir = Path("./tmp")
        tmp_dir.mkdir(exist_ok=True)

        # 设置输出路径
        output_filename = f"replaced_{os.path.basename(state['markdown_path'])}"
        output_path = tmp_dir / output_filename

        # 确保输出路径是字符串
        output_path_str = str(output_path)

        # 替换引用
        replacements = {old_key: new_key}
        validator = CitationValidator()
        replaced_path = validator.replace_citations_in_file(
            state["markdown_path"], replacements, output_path_str
        )

        # 读取替换后的文件内容
        with open(replaced_path, "r", encoding="utf-8") as f:
            replaced_content = f.read()

        result = f"成功替换引用: [@{old_key}] → [@{new_key}]"
        return result, replaced_path, replaced_content

    except Exception as e:
        return f"替换失败: {str(e)}", None, None


# 处理函数: 批量替换所有引用
def replace_all_citations(state):
    if not state or "missing_keys" not in state or "suggestions" not in state:
        return "请先验证引用", None

    suggestions = state.get("suggestions", {})
    replacements = {}

    # 检查每个缺失的引用键是否有建议
    for key in state.get("missing_keys", []):
        if key in suggestions and suggestions[key]:
            # 使用第一个建议作为替换值
            replacements[key] = suggestions[key][0]

    if not replacements:
        return "没有可用的建议引用键进行替换", None

    try:
        # 创建临时目录
        tmp_dir = Path("./tmp")
        tmp_dir.mkdir(exist_ok=True)

        # 设置输出路径
        output_filename = f"replaced_all_{os.path.basename(state['markdown_path'])}"
        output_path = tmp_dir / output_filename

        # 确保输出路径是字符串
        output_path_str = str(output_path)

        # 替换所有引用
        validator = CitationValidator()
        replaced_path = validator.replace_citations_in_file(
            state["markdown_path"], replacements, output_path_str
        )

        # 读取替换后的文件内容
        with open(replaced_path, "r", encoding="utf-8") as f:
            replaced_content = f.read()

        replacements_str = ", ".join(
            [f"[@{old}] → [@{new}]" for old, new in replacements.items()]
        )
        result = f"成功批量替换引用:\n{replacements_str}"

        return result, replaced_path, replaced_content

    except Exception as e:
        return f"批量替换失败: {str(e)}", None, None


# 处理函数: 批量替换表格中选中的引用
def batch_replace_table_citations(table_data, state):
    if not state or not table_data:
        return "没有选中的引用键可以替换", None

    replacements = {}

    # 从表格数据中获取选中的替换项
    for row in table_data:
        if len(row) >= 3 and row[2] and row[1]:  # 如果第三列为True（选中）且有建议值
            old_key = row[0]
            new_key = row[1]
            replacements[old_key] = new_key

    if not replacements:
        return "没有选中有效的替换项", None

    try:
        # 创建临时目录
        tmp_dir = Path("./tmp")
        tmp_dir.mkdir(exist_ok=True)

        # 设置输出路径
        output_filename = f"batch_replaced_{os.path.basename(state['markdown_path'])}"
        output_path = tmp_dir / output_filename

        # 确保输出路径是字符串
        output_path_str = str(output_path)

        # 替换所有选中的引用
        validator = CitationValidator()
        replaced_path = validator.replace_citations_in_file(
            state["markdown_path"], replacements, output_path_str
        )

        # 读取替换后的文件内容
        with open(replaced_path, "r", encoding="utf-8") as f:
            replaced_content = f.read()

        replacements_str = ", ".join(
            [f"[@{old}] → [@{new}]" for old, new in replacements.items()]
        )
        result = f"成功批量替换选中的引用:\n{replacements_str}"

        return result, replaced_path, replaced_content

    except Exception as e:
        return f"批量替换失败: {str(e)}", None, None


# 处理函数: 生成DOCX
def handle_generate_docx(
    current_md_content, current_bib_content, base_name_spec, json_file_obj
):
    if not current_md_content:
        return None, "错误：Markdown内容为空，无法生成DOCX。"

    _base_name = get_base_name(json_file_obj, base_name_spec)
    log_messages = []

    temp_md_path = save_to_temp_file(
        current_md_content, ".md", base_name_spec, json_file_obj
    )
    temp_bib_path = None
    if (
        current_bib_content
    ):  # Bib file is optional for some pandoc use cases but good to have if present
        temp_bib_path = save_to_temp_file(
            current_bib_content, ".bib", base_name_spec, json_file_obj
        )

    if not temp_md_path:
        return None, "错误：无法创建临时Markdown文件。"

    try:
        # handler_convert_to_docx needs md_file and bib_file paths
        docx_output_name = f"{_base_name}_converted"
        docx_file_path, msg = handler_convert_to_docx(
            temp_md_path,
            temp_bib_path,  # Can be None if no bib content
            docx_output_name,
        )
        log_messages.append(msg)

        # Clean up temp files
        if temp_md_path and os.path.exists(temp_md_path):
            os.unlink(temp_md_path)
        if temp_bib_path and os.path.exists(temp_bib_path):
            os.unlink(temp_bib_path)

        if "错误" not in msg and docx_file_path:
            return docx_file_path, "\n".join(log_messages)
        else:
            return None, "\n".join(log_messages)
    except Exception as e:
        if temp_md_path and os.path.exists(temp_md_path):
            os.unlink(temp_md_path)
        if temp_bib_path and os.path.exists(temp_bib_path):
            os.unlink(temp_bib_path)
        return None, f"生成DOCX时发生意外错误: {str(e)}"


# 处理函数: 保存Markdown
def handle_save_md(current_md_content, base_name_spec, json_file_obj):
    if not current_md_content:
        return None, "Markdown内容为空。"

    file_path = save_to_temp_file(
        current_md_content, ".md", base_name_spec, json_file_obj
    )
    if file_path:
        return file_path, "Markdown文件已准备好下载。"
    else:
        return None, "保存Markdown文件失败。"


# 处理函数: 保存BibTeX
def handle_save_bib(current_bib_content, base_name_spec, json_file_obj):
    if not current_bib_content:
        return None, "BibTeX内容为空。"

    file_path = save_to_temp_file(
        current_bib_content, ".bib", base_name_spec, json_file_obj
    )
    if file_path:
        return file_path, "BibTeX文件已准备好下载。"
    else:
        return None, "保存BibTeX文件失败。"


# 更新引用下拉列表函数
def update_missing_cite_dropdown(state):
    if not state or "missing_keys" not in state:
        return {"choices": []}, {"choices": []}

    missing_keys = state.get("missing_keys", [])
    missing_cite_choices = [f"[@{key}]" for key in missing_keys]

    return {"choices": missing_cite_choices}, {"choices": []}


# 更新建议下拉列表函数
def update_suggested_cite_dropdown(missing_cite, state):
    if not missing_cite or not state or "suggestions" not in state:
        return {"choices": []}

    # 从选择的引用中提取键
    key = missing_cite.strip("[@]")
    suggestions = state.get("suggestions", {}).get(key, [])

    if not suggestions:
        return {"choices": [], "value": None}

    suggested_choices = [f"[@{s}]" for s in suggestions]
    return {
        "choices": suggested_choices,
        "value": suggested_choices[0] if suggested_choices else None,
    }
