"""
无序列表转换为多级标题标签页
"""

import gradio as gr

from ..markdown_handlers import process_lists_to_headings


def create_lists_to_headings_tab() -> gr.TabItem:
    """
    创建无序列表转换为多级标题标签页

    Returns:
        无序列表转换为多级标题标签页组件
    """
    with gr.TabItem("无序列表转换标题") as tab:
        gr.Markdown(
            "将带有加粗小标题的无序列表转换为多级标题。例如：\n\n"
            "```\n- **标题:** 内容\n```\n\n将被转换为：\n\n"
            "```\n### 标题\n内容\n```"
        )

        with gr.Row():
            with gr.Column():
                md_file_input = gr.File(
                    label="上传Markdown文件",
                    file_count="single",
                    file_types=[".md", ".markdown", ".txt"],
                    type="filepath",
                )
                md_output_name = gr.Textbox(label="输出文件名（不含扩展名，可选）")

                md_process_btn = gr.<PERSON><PERSON>("转换列表为标题", variant="primary")
                md_result_info = gr.Textbox(label="处理结果", interactive=False)

            with gr.Column():
                md_input_text = gr.Textbox(
                    label="Markdown内容",
                    lines=10,
                    placeholder="也可以直接粘贴Markdown内容到这里...",
                )
                md_output_text = gr.Textbox(label="处理后的内容", lines=10)
                md_download_file = gr.File(label="下载处理后的文件")

        # 处理逻辑
        md_process_btn.click(
            fn=process_lists_to_headings,
            inputs=[md_file_input, md_input_text, md_output_name],
            outputs=[md_output_text, md_download_file, md_result_info],
        )

    return tab
