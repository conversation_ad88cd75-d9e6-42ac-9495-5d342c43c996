"""
翻译功能标签页
"""

import gradio as gr

from ..handlers import translate_text, translate_file_content


def create_translation_tab() -> gr.TabItem:
    """
    创建翻译功能标签页

    Returns:
        翻译功能标签页组件
    """
    with gr.TabItem("文档翻译") as tab:
        gr.Markdown("使用大语言模型翻译文本或文件，支持多种语言和专业领域。")

        with gr.Tabs() as translate_tabs:
            # 文本翻译标签页
            with gr.TabItem("文本翻译"):
                with gr.Row():
                    with gr.Column():
                        text_input = gr.Textbox(
                            label="输入文本",
                            lines=10,
                            placeholder="请输入要翻译的文本...",
                        )

                        with gr.Accordion("翻译设置", open=False):
                            with gr.Row():
                                source_lang = gr.Textbox(
                                    label="源语言",
                                    value="English",
                                    placeholder="例如: English, Chinese, Japanese...",
                                )
                                target_lang = gr.Textbox(
                                    label="目标语言",
                                    value="Chinese",
                                    placeholder="例如: English, Chinese, Japanese...",
                                )

                            with gr.Row():
                                domain = gr.Textbox(
                                    label="专业领域（可选）",
                                    placeholder="例如: Medical, Legal, IT...",
                                )
                                model = gr.Textbox(
                                    label="模型名称",
                                    value="gemma3:4b",
                                    placeholder="例如: qwen2.5:32b, llama3:8b...",
                                )

                            with gr.Row():
                                base_url = gr.Textbox(
                                    label="API地址",
                                    value="http://localhost:11434/v1",
                                    placeholder="例如: http://localhost:11434/v1",
                                )
                                api_key = gr.Textbox(
                                    label="API密钥",
                                    value="ollama",
                                    placeholder="例如: ollama, sk-...",
                                )

                            with gr.Row():
                                temperature = gr.Slider(
                                    label="温度",
                                    minimum=0.0,
                                    maximum=1.0,
                                    value=0.3,
                                    step=0.1,
                                )
                                top_p = gr.Slider(
                                    label="Top-P",
                                    minimum=0.0,
                                    maximum=1.0,
                                    value=0.9,
                                    step=0.1,
                                )

                        translate_btn = gr.Button("翻译文本", variant="primary")
                        text_result_info = gr.Textbox(
                            label="处理结果", interactive=False
                        )

                    with gr.Column():
                        text_output = gr.Textbox(label="翻译结果", lines=10)

            # 文件翻译标签页
            with gr.TabItem("文件翻译"):
                with gr.Row():
                    with gr.Column():
                        translate_file_input = gr.File(
                            label="上传要翻译的文本文件",
                            file_count="single",
                            file_types=[".txt", ".md", ".markdown"],
                            type="filepath",
                        )
                        file_output_name = gr.Textbox(
                            label="输出文件名（不含扩展名，可选）"
                        )

                        with gr.Accordion("翻译设置", open=False):
                            with gr.Row():
                                file_source_lang = gr.Textbox(
                                    label="源语言",
                                    value="English",
                                    placeholder="例如: English, Chinese, Japanese...",
                                )
                                file_target_lang = gr.Textbox(
                                    label="目标语言",
                                    value="Chinese",
                                    placeholder="例如: English, Chinese, Japanese...",
                                )

                            with gr.Row():
                                file_domain = gr.Textbox(
                                    label="专业领域（可选）",
                                    placeholder="例如: Medical, Legal, IT...",
                                )
                                file_model = gr.Textbox(
                                    label="模型名称",
                                    value="gemma3:4b",
                                    placeholder="例如: qwen2.5:32b, llama3:8b...",
                                )

                            with gr.Row():
                                file_base_url = gr.Textbox(
                                    label="API地址",
                                    value="http://localhost:11434/v1",
                                    placeholder="例如: http://localhost:11434/v1",
                                )
                                file_api_key = gr.Textbox(
                                    label="API密钥",
                                    value="ollama",
                                    placeholder="例如: ollama, sk-...",
                                )

                            with gr.Row():
                                file_temperature = gr.Slider(
                                    label="温度",
                                    minimum=0.0,
                                    maximum=1.0,
                                    value=0.3,
                                    step=0.1,
                                )
                                file_top_p = gr.Slider(
                                    label="Top-P",
                                    minimum=0.0,
                                    maximum=1.0,
                                    value=0.9,
                                    step=0.1,
                                )

                            with gr.Row():
                                batch_size = gr.Slider(
                                    label="批处理大小",
                                    minimum=1,
                                    maximum=20,
                                    value=5,
                                    step=1,
                                )
                                max_tokens = gr.Slider(
                                    label="最大Token数",
                                    minimum=1000,
                                    maximum=10000,
                                    value=4000,
                                    step=500,
                                )
                                max_requests = gr.Slider(
                                    label="最大并发请求数",
                                    minimum=1,
                                    maximum=20,
                                    value=5,
                                    step=1,
                                )

                        file_translate_btn = gr.Button("翻译文件", variant="primary")
                        file_result_info = gr.Textbox(
                            label="处理结果", interactive=False
                        )

                    with gr.Column():
                        file_original = gr.Textbox(label="原文内容", lines=8)
                        file_translated = gr.Textbox(label="翻译内容", lines=8)
                        file_download = gr.File(label="下载翻译后的文件")

        # 文本翻译逻辑
        translate_btn.click(
            fn=translate_text,
            inputs=[
                text_input,
                base_url,
                api_key,
                model,
                source_lang,
                target_lang,
                domain,
                temperature,
                top_p,
            ],
            outputs=[text_output, text_result_info],
        )

        # 文件翻译逻辑
        file_translate_btn.click(
            fn=translate_file_content,
            inputs=[
                translate_file_input,
                file_base_url,
                file_api_key,
                file_model,
                file_source_lang,
                file_target_lang,
                file_domain,
                file_temperature,
                file_top_p,
                batch_size,
                max_tokens,
                max_requests,
                file_output_name,
            ],
            outputs=[file_original, file_translated, file_download, file_result_info],
        )

    return tab
