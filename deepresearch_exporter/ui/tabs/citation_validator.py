"""
引用验证选项卡：验证 Markdown 文件中的引用是否在 BibTeX 文件中存在，提供替换选项
"""

import os
import re
from pathlib import Path
import gradio as gr
import pandas as pd

from ...core.validator import CitationValidator
from ...logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def extract_citations(markdown_path):
    """从Markdown文件中提取引用标记"""
    try:
        with open(markdown_path, "r", encoding="utf-8") as f:
            content = f.read()

        citation_pattern = r"\[@([^\]]+)\]"
        citations = re.findall(citation_pattern, content)
        return citations
    except Exception as e:
        logger.error(f"提取引用时出错: {str(e)}")
        return []


def detect_missing_citations(markdown_path, bibtex_path):
    """检测缺失的引用"""
    try:
        # 使用不带高亮的验证函数
        validator = CitationValidator()
        is_valid, missing_citations, suggestions = validator.validate_citations(markdown_path, bibtex_path)

        return missing_citations, suggestions
    except Exception as e:
        logger.error(f"检测缺失引用时出错: {str(e)}")
        return [], {}


def apply_citation_replacements(markdown_path, replacements, output_path):
    """应用引用替换"""
    try:
        with open(markdown_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 应用替换
        for old_key, new_key in replacements.items():
            if not new_key:  # 如果替换值为空，则跳过
                continue

            # 替换[@old_key]格式的引用
            pattern = r"\[@" + re.escape(old_key) + r"\]"
            content = re.sub(pattern, f"[@{new_key}]", content)

        # 写入输出文件
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(content)

        return output_path
    except Exception as e:
        logger.error(f"应用引用替换时出错: {str(e)}")
        raise e


def create_citation_validator_tab():
    """创建引用验证标签页"""

    with gr.Tab("引用验证"):
        gr.Markdown("""
        ### 引用验证与替换工具
        
        检测 Markdown 文档中缺失的引用标记，提供替换选项。
        """)

        # 文件上传区域
        with gr.Row():
            with gr.Column(scale=1):
                markdown_file = gr.File(
                    label="选择 Markdown 文件", file_types=[".md", ".markdown", ".txt"]
                )
                bibtex_file = gr.File(
                    label="选择 BibTeX 文件", file_types=[".bib", ".bibtex"]
                )
                check_button = gr.Button("检查缺失引用", variant="primary")

            with gr.Column(scale=1):
                status_display = gr.Markdown(label="状态")

        # 缺失引用编辑区域
        with gr.Row(visible=False) as missing_citations_area:
            with gr.Column():
                gr.Markdown("### 缺失的引用及替换建议")
                missing_citations_table = gr.Dataframe(
                    headers=["缺失的引用键", "替换为"],
                    datatype=["str", "str"],
                    label="编辑下方的'替换为'列以修改替换值",
                    interactive=True,
                    row_count=10,
                    col_count=(2, "fixed"),
                )
                apply_button = gr.Button("应用替换", variant="primary")

        # 状态存储
        state = gr.State({})

        # 输出文件下载
        result_area = gr.Row(visible=False)
        with result_area:
            replaced_file = gr.File(label="下载替换后的文件")
            markdown_preview = gr.Markdown(label="替换预览")

        # 处理函数
        def check_citations(markdown_file, bibtex_file):
            if not markdown_file or not bibtex_file:
                return (
                    "请上传 Markdown 文件和 BibTeX 文件",
                    {},
                    None,
                    gr.Row(visible=False),
                )

            try:
                # 获取上传文件的路径
                markdown_path = markdown_file.name
                bibtex_path = bibtex_file.name

                # 检测缺失引用
                missing_keys, suggestions = detect_missing_citations(
                    markdown_path, bibtex_path
                )

                if not missing_keys:
                    return (
                        "✅ 没有发现缺失的引用！文档中的所有引用都在BibTeX文件中有对应条目。",
                        {},
                        None,
                        gr.Row(visible=False),
                    )

                # 生成状态信息
                state_info = {
                    "markdown_path": markdown_path,
                    "bibtex_path": bibtex_path,
                    "missing_keys": missing_keys,
                    "suggestions": suggestions,
                }

                # 准备表格数据
                table_data = []
                for key in missing_keys:
                    suggestion_list = suggestions.get(key, [])
                    suggested_key = suggestion_list[0] if suggestion_list else ""
                    table_data.append([key, suggested_key])

                # 生成报告
                keys_count = len(missing_keys)
                report = f"❌ 发现 {keys_count} 个缺失的引用，请在下面编辑替换值"

                return (
                    report,
                    state_info,
                    pd.DataFrame(table_data, columns=["缺失的引用键", "替换为"]),
                    gr.Row(visible=True),
                )

            except Exception as e:
                logger.error(f"检查引用时出错: {str(e)}")
                return f"处理失败: {str(e)}", {}, None, gr.Row(visible=False)

        def apply_replacements(table_data, state_info):
            if not state_info or not state_info.get("missing_keys"):
                return "请先检查缺失引用", None, "", gr.Row(visible=False)

            if table_data is None or len(table_data) == 0:
                return "没有找到缺失引用数据", None, "", gr.Row(visible=False)

            try:
                # 创建替换表
                replacements = {}
                for _, row in table_data.iterrows():
                    if len(row) >= 2:
                        old_key = row[0]
                        new_key = row[1]
                        if new_key:  # 只替换有值的项
                            replacements[old_key] = new_key

                if not replacements:
                    return "没有提供有效的替换值", None, "", gr.Row(visible=False)

                # 创建临时目录
                tmp_dir = Path("./tmp")
                tmp_dir.mkdir(exist_ok=True)

                # 设置输出路径
                output_filename = (
                    f"replaced_{os.path.basename(state_info['markdown_path'])}"
                )
                output_path = tmp_dir / output_filename
                output_path_str = str(output_path)

                # 应用替换
                replaced_path = apply_citation_replacements(
                    state_info["markdown_path"], replacements, output_path_str
                )

                # 读取替换后的内容用于预览
                with open(replaced_path, "r", encoding="utf-8") as f:
                    replaced_content = f.read()

                # 计算替换项数量
                replaced_count = len(replacements)
                result_msg = f"✅ 已成功替换 {replaced_count} 项引用"

                return result_msg, replaced_path, replaced_content, gr.Row(visible=True)

            except Exception as e:
                logger.error(f"应用替换时出错: {str(e)}")
                return f"替换失败: {str(e)}", None, "", gr.Row(visible=False)

        # 连接函数
        check_button.click(
            check_citations,
            inputs=[markdown_file, bibtex_file],
            outputs=[
                status_display,
                state,
                missing_citations_table,
                missing_citations_area,
            ],
        )

        apply_button.click(
            apply_replacements,
            inputs=[missing_citations_table, state],
            outputs=[status_display, replaced_file, markdown_preview, result_area],
        )

        # 使用说明
        with gr.Accordion("使用说明", open=False):
            gr.Markdown("""
            ### 使用方法
            
            1. 上传包含引用标记的 Markdown 文件（引用格式：`[@citekey]`）
            2. 上传包含引用信息的 BibTeX 文件
            3. 点击"检查缺失引用"按钮
            4. 如果有缺失的引用，在表格中编辑"替换为"列的值
               - 系统会自动填入推荐的替换值（如有）
               - 您可以根据需要修改替换值
               - 如果不需要替换某项，可以将其替换值置空
            5. 点击"应用替换"按钮执行替换操作
            6. 下载替换后的文件并查看预览
            
            ### 示例
            
            Markdown 文件中有引用 `[@missing2023]`，但 BibTeX 文件中没有对应条目。
            系统可能会推荐替换为 `[@smith2023]`，您可以接受这个建议或输入其他值。
            应用替换后，文档中的 `[@missing2023]` 将被替换为您指定的引用键。
            """)
