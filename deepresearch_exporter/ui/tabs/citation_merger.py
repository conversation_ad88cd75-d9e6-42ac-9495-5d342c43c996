"""
引用整合选项卡：整合BibTeX引用到Markdown文档
"""

import os
import re
import tempfile
from pathlib import Path
import gradio as gr
import json

from ...tools.bibtex import BibtexProcessor
from ...logger import setup_logger

# 配置日志
logger = setup_logger(name=__name__)


def create_citation_merger_tab():
    """创建引用整合标签页"""

    with gr.Tab("引用整合"):
        gr.Markdown("""
        ### 功能说明
        
        将Markdown文档中的引用标记`[@citekey]`替换为链接`[n](url)`，链接地址来自BibTeX文件中的URL字段。
        
        支持的引用标记格式：`[@citekey]`，其中citekey是BibTeX文件中的引用键。
        """)

        # 文件上传区域
        gr.Markdown("### 上传文件")
        with gr.Row():
            markdown_file = gr.File(
                label="选择Markdown文件", file_types=[".md", ".markdown", ".txt"]
            )
            bibtex_file = gr.File(
                label="选择BibTeX文件", file_types=[".bib", ".bibtex"]
            )

        # 选项设置
        gr.Markdown("### 选项设置")
        url_deduplicate = gr.Checkbox(
            label="URL去重", value=False, info="如果多个引用指向同一URL，使用相同的编号"
        )
        
        complete_deduplicate = gr.Checkbox(
            label="完全去重", 
            value=False, 
            info="启用后，相邻的相同URL引用将被合并为一个，避免连续出现相同标签"
        )

        link_text_pattern = gr.Textbox(
            label="链接文本模式", value="[{index}]", info="使用{index}作为序号占位符"
        )

        append_references = gr.Checkbox(
            label="添加参考文献列表", value=False, info="在文档末尾添加参考文献列表"
        )

        # 添加日志显示区域
        log_output = gr.Textbox(label="处理日志", lines=5)

        # 添加BibTeX条目展示区域
        bib_entries = gr.Dataframe(
            headers=["引用键", "类型", "标题", "作者", "年份", "URL", "DOI", "期刊"],
            datatype=["str", "str", "str", "str", "str", "str", "str", "str"],
            label="BibTeX条目",
            visible=False,
        )

        # 输出区域
        output_text = gr.Textbox(label="处理后的内容", lines=10)
        output_file = gr.File(label="下载处理后的文件")

        # 预览BibTeX按钮
        preview_button = gr.Button("预览BibTeX条目")

        # 处理按钮和处理函数
        process_button = gr.Button("处理引用")

        def preview_bibtex(bibtex_file):
            if not bibtex_file:
                return "请上传BibTeX文件", gr.update(visible=False)

            try:
                log_messages = []
                log_messages.append(f"开始读取BibTeX文件: {bibtex_file.name}")

                # 获取上传文件的路径
                bibtex_path = bibtex_file.name

                # 加载BibTeX文件
                bibtex_processor = BibtexProcessor()
                citations = bibtex_processor.load_bibtex_entries(bibtex_path)
                log_messages.append(f"成功读取 {len(citations)} 个BibTeX条目")

                # 准备用于展示的数据
                entries_data = []
                for key, entry in citations.items():
                    authors_str = ", ".join(entry.get("authors", []))[:50]
                    if len(authors_str) > 50:
                        authors_str += "..."

                    title = entry.get("title", "")[:50]
                    if len(title) > 50:
                        title += "..."

                    entries_data.append(
                        [
                            key,
                            entry.get("type", ""),
                            title,
                            authors_str,
                            entry.get("year", ""),
                            entry.get("url", "")[:50],
                            entry.get("doi", ""),
                            entry.get("journal", "")[:50],
                        ]
                    )

                log_messages.append("BibTeX条目加载完成，请查看下方表格")

                return "\n".join(log_messages), gr.update(
                    value=entries_data, visible=True
                )

            except Exception as e:
                return f"处理失败：{str(e)}", gr.update(visible=False)

        def process_citation(
            markdown_file,
            bibtex_file,
            url_deduplicate,
            complete_deduplicate,
            link_text_pattern,
            append_references,
        ):
            if not markdown_file or not bibtex_file:
                return "请上传Markdown文件和BibTeX文件", None, "处理失败：缺少必要文件"

            try:
                log_messages = []
                log_messages.append(f"开始处理文件...")
                log_messages.append(f"Markdown文件: {markdown_file.name}")
                log_messages.append(f"BibTeX文件: {bibtex_file.name}")

                # 创建临时目录
                tmp_dir = Path("./tmp")
                tmp_dir.mkdir(exist_ok=True)
                log_messages.append(f"创建临时目录: {tmp_dir}")

                # 获取上传文件的路径
                markdown_path = markdown_file.name
                bibtex_path = bibtex_file.name

                # 设置输出路径
                output_filename = f"processed_{os.path.basename(markdown_path)}"
                output_path = tmp_dir / output_filename
                log_messages.append(f"输出文件: {output_path}")

                # 处理引用替换
                log_messages.append("开始处理引用替换...")
                # 加载BibTeX文件
                bibtex_processor = BibtexProcessor()
                citations = bibtex_processor.load_bibtex_entries(bibtex_path)
                log_messages.append(f"读取到 {len(citations)} 个BibTeX条目")

                # 附加参考文献选项
                if append_references:
                    log_messages.append("已启用参考文献列表附加选项")
                
                # 去重选项
                if url_deduplicate:
                    log_messages.append("已启用URL去重选项：相同URL的引用将使用相同的编号")
                
                if complete_deduplicate:
                    log_messages.append("已启用完全去重选项：相邻的相同URL引用将被合并为一个")

                result_path = bibtex_processor.merge_markdown_with_bibtex(
                    markdown_path,
                    bibtex_path,
                    str(output_path),
                    url_deduplicate=url_deduplicate,
                    complete_deduplicate=complete_deduplicate,
                    link_text_pattern=link_text_pattern,
                    append_references=append_references,
                )

                log_messages.append(f"引用替换处理完成")
                log_messages.append(f"结果已保存到: {result_path}")

                # 读取处理结果
                with open(result_path, "r", encoding="utf-8") as f:
                    processed_content = f.read()

                # 返回处理结果和下载文件
                return processed_content, str(output_path), "\n".join(log_messages)

            except Exception as e:
                return f"处理失败：{str(e)}", None, f"处理失败：{str(e)}"

        # 连接预览按钮
        preview_button.click(
            fn=preview_bibtex, inputs=[bibtex_file], outputs=[log_output, bib_entries]
        )

        # 连接处理按钮
        process_button.click(
            fn=process_citation,
            inputs=[
                markdown_file,
                bibtex_file,
                url_deduplicate,
                complete_deduplicate,
                link_text_pattern,
                append_references,
            ],
            outputs=[output_text, output_file, log_output],
        )

        # 使用说明
        with gr.Accordion("使用说明", open=False):
            gr.Markdown("""
            ### 使用方法
            
            1. 上传包含引用标记的Markdown文件
            2. 上传包含引用信息的BibTeX文件
            3. 点击"预览BibTeX条目"可以查看读取到的引用信息
            4. 选择是否进行URL去重
            5. 自定义链接文本格式（默认为`[索引]`）
            6. 选择是否在文档末尾添加参考文献列表
            7. 点击"处理引用"按钮
            8. 处理日志会显示在日志区域
            9. 下载处理后的Markdown文件
            
            ### 示例
            
            Markdown文件中的引用：
            ```markdown
            根据研究[@smith2023]，大型语言模型在训练规模扩大后表现出新的涌现能力。
            ```
            
            BibTeX文件中的条目：
            ```bibtex
            @article{smith2023,
              author = {Smith, John},
              title = {Emergent Abilities of Large Language Models},
              journal = {AI Journal},
              year = {2023},
              url = {https://example.com/papers/smith2023}
            }
            ```
            
            处理后的Markdown（不添加参考文献列表）：
            ```markdown
            根据研究[1](https://example.com/papers/smith2023)，大型语言模型在训练规模扩大后表现出新的涌现能力。
            ```
            
            处理后的Markdown（添加参考文献列表）：
            ```markdown
            根据研究[1](https://example.com/papers/smith2023)，大型语言模型在训练规模扩大后表现出新的涌现能力。
            
            ## 参考文献
            
            1. Smith, John. *Emergent Abilities of Large Language Models*. AI Journal, 2023. [链接](https://example.com/papers/smith2023)
            ```
            """)
