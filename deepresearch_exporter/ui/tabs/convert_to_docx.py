"""
转换为DOCX标签页
"""

import gradio as gr

from ..handlers import convert_to_docx


def create_convert_to_docx_tab() -> gr.TabItem:
    """
    创建转换为DOCX标签页

    Returns:
        转换为DOCX标签页组件
    """
    with gr.TabItem("转换为DOCX") as tab:
        gr.Markdown("将已有的Markdown和BibTeX文件转换为DOCX格式。")

        with gr.Row():
            with gr.Column():
                convert_md_file = gr.File(
                    label="上传Markdown文件",
                    file_count="single",
                    file_types=[".md", ".markdown", ".txt"],
                    type="filepath",
                )
                convert_bib_file = gr.File(
                    label="上传BibTeX文件",
                    file_count="single",
                    file_types=[".bib", ".bibtex", ".txt"],
                    type="filepath",
                )
                convert_output_name = gr.Textbox(label="输出文件名（不含扩展名，可选）")
                convert_csl_file = gr.File(
                    label="CSL引用样式文件（可选）",
                    file_count="single",
                    file_types=[".csl"],
                    type="filepath",
                )
                convert_ref_doc_file = gr.File(
                    label="DOCX参考文档（可选）",
                    file_count="single",
                    file_types=[".docx"],
                    type="filepath",
                )
                convert_btn = gr.Button("转换为DOCX", variant="primary")
                convert_result_info = gr.Textbox(label="处理结果", interactive=False)

            with gr.Column():
                convert_docx_download = gr.File(label="下载DOCX文件")

        # 处理逻辑
        convert_btn.click(
            fn=convert_to_docx,
            inputs=[
                convert_md_file,
                convert_bib_file,
                convert_output_name,
                convert_csl_file,
                convert_ref_doc_file,
            ],
            outputs=[convert_docx_download, convert_result_info],
        )

    return tab
