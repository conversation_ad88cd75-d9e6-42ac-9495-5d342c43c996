"""
在线翻译功能标签页
"""

import os
import gradio as gr

from ..handlers import online_translate_text, online_translate_file_content


def create_online_translation_tab() -> gr.TabItem:
    """
    创建在线翻译功能标签页

    Returns:
        在线翻译功能标签页组件
    """
    # 从环境变量中获取DeepL API密钥
    default_deepl_api_key = os.environ.get("DEEPL_API_KEY", "")

    with gr.TabItem("在线翻译") as tab:
        gr.Markdown("""使用Google Translate或DeepL API翻译文本或文件，无需本地LLM环境。
        
- Google Translate：无需API密钥，但可能有流量限制
- DeepL API：需要API密钥，但翻译质量更高
  - 可在.env文件中设置DEEPL_API_KEY，将自动读取
        """)

        with gr.Tabs() as translate_tabs:
            # 文本翻译标签页
            with gr.TabItem("文本翻译"):
                with gr.<PERSON>():
                    with gr.Column():
                        text_input = gr.Textbox(
                            label="输入文本",
                            lines=10,
                            placeholder="请输入要翻译的文本...",
                        )

                        with gr.Accordion("翻译设置", open=True):
                            translator_type = gr.Radio(
                                choices=["google", "deepl"],
                                value="google",
                                label="翻译服务",
                                info="Google Translate（无需API密钥）或DeepL API（需要API密钥）",
                            )

                            api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="DeepL API密钥（使用Google Translate时可为空）",
                                type="password",
                                value=default_deepl_api_key,
                                visible=False,
                            )

                            with gr.Row():
                                source_lang = gr.Dropdown(
                                    label="源语言",
                                    choices=[
                                        "auto",
                                        "en",
                                        "zh-CN",
                                        "ja",
                                        "ko",
                                        "fr",
                                        "de",
                                        "es",
                                        "ru",
                                        "pt",
                                        "it",
                                        "bg",
                                        "cs",
                                        "da",
                                        "el",
                                        "et",
                                        "fi",
                                        "hu",
                                        "id",
                                        "lt",
                                        "lv",
                                        "nb",
                                        "nl",
                                        "pl",
                                        "ro",
                                        "sk",
                                        "sl",
                                        "sv",
                                        "tr",
                                        "uk",
                                    ],
                                    value="auto",
                                    info="自动检测或指定源语言",
                                    allow_custom_value=True,
                                )
                                target_lang = gr.Dropdown(
                                    label="目标语言",
                                    choices=[
                                        "zh-CN",
                                        "en",
                                        "en-US",
                                        "en-GB",
                                        "ja",
                                        "ko",
                                        "fr",
                                        "de",
                                        "es",
                                        "ru",
                                        "pt",
                                        "pt-BR",
                                        "pt-PT",
                                        "it",
                                        "bg",
                                        "cs",
                                        "da",
                                        "el",
                                        "et",
                                        "fi",
                                        "hu",
                                        "id",
                                        "lt",
                                        "lv",
                                        "nb",
                                        "nl",
                                        "pl",
                                        "ro",
                                        "sk",
                                        "sl",
                                        "sv",
                                        "tr",
                                        "uk",
                                    ],
                                    value="zh-CN",
                                    info="目标语言",
                                    allow_custom_value=True,
                                )

                            # DeepL特有选项
                            formality = gr.Radio(
                                choices=["default", "more", "less"],
                                value="default",
                                label="语言风格（仅DeepL）",
                                info="default=标准，more=更正式，less=更随意",
                                visible=False,
                            )

                            with gr.Row(visible=False) as deepl_advanced_row:
                                preserve_formatting = gr.Checkbox(
                                    value=False,
                                    label="保留格式",
                                    info="保留文本的原始格式，如标点和大小写",
                                )

                                split_sentences = gr.Dropdown(
                                    choices=["nonewlines", "default", "no"],
                                    value="nonewlines",
                                    label="分句策略",
                                    info="nonewlines=仅在换行处分句，default=默认分句，no=不分句",
                                )

                            with gr.Row(visible=False) as deepl_model_row:
                                tag_handling = gr.Dropdown(
                                    choices=["", "xml", "html"],
                                    value="",
                                    label="标签处理",
                                    info="处理文本中包含的XML或HTML标签",
                                )

                                model_type = gr.Dropdown(
                                    choices=[
                                        "",
                                        "quality_optimized",
                                        "prefer_quality_optimized",
                                    ],
                                    value="",
                                    label="模型类型",
                                    info="选择DeepL翻译模型类型",
                                )

                            # 当翻译服务类型改变时，显示或隐藏相关选项
                            def update_translator_options(choice):
                                if choice == "deepl":
                                    return (
                                        gr.update(visible=True),  # api_key
                                        gr.update(visible=True),  # formality
                                        gr.update(visible=True),  # deepl_advanced_row
                                        gr.update(visible=True),  # deepl_model_row
                                    )
                                else:
                                    return (
                                        gr.update(visible=False),  # api_key
                                        gr.update(visible=False),  # formality
                                        gr.update(visible=False),  # deepl_advanced_row
                                        gr.update(visible=False),  # deepl_model_row
                                    )

                            translator_type.change(
                                fn=update_translator_options,
                                inputs=[translator_type],
                                outputs=[
                                    api_key,
                                    formality,
                                    deepl_advanced_row,
                                    deepl_model_row,
                                ],
                            )

                        translate_btn = gr.Button("翻译文本", variant="primary")
                        text_result_info = gr.Textbox(
                            label="处理结果", interactive=False
                        )

                    with gr.Column():
                        text_output = gr.Textbox(label="翻译结果", lines=10)

            # 文件翻译标签页
            with gr.TabItem("文件翻译"):
                with gr.Row():
                    with gr.Column():
                        translate_file_input = gr.File(
                            label="上传要翻译的文本文件",
                            file_count="single",
                            file_types=[".txt", ".md", ".markdown"],
                            type="filepath",
                        )
                        file_output_name = gr.Textbox(
                            label="输出文件名（不含扩展名，可选）"
                        )

                        with gr.Accordion("翻译设置", open=True):
                            file_translator_type = gr.Radio(
                                choices=["google", "deepl"],
                                value="google",
                                label="翻译服务",
                                info="Google Translate（无需API密钥）或DeepL API（需要API密钥）",
                            )

                            file_api_key = gr.Textbox(
                                label="API密钥",
                                placeholder="DeepL API密钥（使用Google Translate时可为空）",
                                type="password",
                                value=default_deepl_api_key,
                                visible=False,
                            )

                            with gr.Row():
                                file_source_lang = gr.Dropdown(
                                    label="源语言",
                                    choices=[
                                        "auto",
                                        "en",
                                        "zh-CN",
                                        "ja",
                                        "ko",
                                        "fr",
                                        "de",
                                        "es",
                                        "ru",
                                        "pt",
                                        "it",
                                        "bg",
                                        "cs",
                                        "da",
                                        "el",
                                        "et",
                                        "fi",
                                        "hu",
                                        "id",
                                        "lt",
                                        "lv",
                                        "nb",
                                        "nl",
                                        "pl",
                                        "ro",
                                        "sk",
                                        "sl",
                                        "sv",
                                        "tr",
                                        "uk",
                                    ],
                                    value="auto",
                                    info="自动检测或指定源语言",
                                    allow_custom_value=True,
                                )
                                file_target_lang = gr.Dropdown(
                                    label="目标语言",
                                    choices=[
                                        "zh-CN",
                                        "en",
                                        "en-US",
                                        "en-GB",
                                        "ja",
                                        "ko",
                                        "fr",
                                        "de",
                                        "es",
                                        "ru",
                                        "pt",
                                        "pt-BR",
                                        "pt-PT",
                                        "it",
                                        "bg",
                                        "cs",
                                        "da",
                                        "el",
                                        "et",
                                        "fi",
                                        "hu",
                                        "id",
                                        "lt",
                                        "lv",
                                        "nb",
                                        "nl",
                                        "pl",
                                        "ro",
                                        "sk",
                                        "sl",
                                        "sv",
                                        "tr",
                                        "uk",
                                    ],
                                    value="zh-CN",
                                    info="目标语言",
                                    allow_custom_value=True,
                                )

                            # DeepL特有选项
                            file_formality = gr.Radio(
                                choices=["default", "more", "less"],
                                value="default",
                                label="语言风格（仅DeepL）",
                                info="default=标准，more=更正式，less=更随意",
                                visible=False,
                            )

                            with gr.Row(visible=False) as file_deepl_advanced_row:
                                file_preserve_formatting = gr.Checkbox(
                                    value=False,
                                    label="保留格式",
                                    info="保留文本的原始格式，如标点和大小写",
                                )

                                file_split_sentences = gr.Dropdown(
                                    choices=["nonewlines", "default", "no"],
                                    value="nonewlines",
                                    label="分句策略",
                                    info="nonewlines=仅在换行处分句，default=默认分句，no=不分句",
                                )

                            with gr.Row(visible=False) as file_deepl_model_row:
                                file_tag_handling = gr.Dropdown(
                                    choices=["", "xml", "html"],
                                    value="",
                                    label="标签处理",
                                    info="处理文本中包含的XML或HTML标签",
                                )

                                file_model_type = gr.Dropdown(
                                    choices=[
                                        "",
                                        "quality_optimized",
                                        "prefer_quality_optimized",
                                    ],
                                    value="",
                                    label="模型类型",
                                    info="选择DeepL翻译模型类型",
                                )

                            # 当翻译服务类型改变时，显示或隐藏相关选项
                            def update_file_translator_options(choice):
                                if choice == "deepl":
                                    return (
                                        gr.update(visible=True),  # file_api_key
                                        gr.update(visible=True),  # file_formality
                                        gr.update(
                                            visible=True
                                        ),  # file_deepl_advanced_row
                                        gr.update(visible=True),  # file_deepl_model_row
                                    )
                                else:
                                    return (
                                        gr.update(visible=False),  # file_api_key
                                        gr.update(visible=False),  # file_formality
                                        gr.update(
                                            visible=False
                                        ),  # file_deepl_advanced_row
                                        gr.update(
                                            visible=False
                                        ),  # file_deepl_model_row
                                    )

                            file_translator_type.change(
                                fn=update_file_translator_options,
                                inputs=[file_translator_type],
                                outputs=[
                                    file_api_key,
                                    file_formality,
                                    file_deepl_advanced_row,
                                    file_deepl_model_row,
                                ],
                            )

                        file_translate_btn = gr.Button("翻译文件", variant="primary")
                        file_result_info = gr.Textbox(
                            label="处理结果", interactive=False
                        )

                    with gr.Column():
                        file_original = gr.Textbox(label="原文内容", lines=8)
                        file_translated = gr.Textbox(label="翻译内容", lines=8)
                        file_download = gr.File(label="下载翻译后的文件")

        # 文本翻译逻辑
        translate_btn.click(
            fn=online_translate_text,
            inputs=[
                text_input,
                translator_type,
                api_key,
                source_lang,
                target_lang,
                formality,
                preserve_formatting,
                split_sentences,
                tag_handling,
                model_type,
            ],
            outputs=[text_output, text_result_info],
        )

        # 文件翻译逻辑
        file_translate_btn.click(
            fn=online_translate_file_content,
            inputs=[
                translate_file_input,
                file_translator_type,
                file_api_key,
                file_source_lang,
                file_target_lang,
                file_formality,
                file_preserve_formatting,
                file_split_sentences,
                file_tag_handling,
                file_model_type,
                file_output_name,
            ],
            outputs=[file_original, file_translated, file_download, file_result_info],
        )

    return tab
