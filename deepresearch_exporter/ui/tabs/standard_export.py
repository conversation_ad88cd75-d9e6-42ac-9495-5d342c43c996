"""
标准导出标签页
"""

import gradio as gr
from typing import Tuple

from ..handlers import process_file, download_files


def create_standard_export_tab() -> gr.TabItem:
    """
    创建标准导出标签页

    Returns:
        标准导出标签页组件
    """
    with gr.TabItem("标准导出") as tab:
        gr.Markdown(
            "上传ChatGPT导出的Deep Research对话JSON文件，转换为Markdown和BibTeX格式。"
        )

        with gr.Row():
            with gr.Column():
                file_input = gr.File(
                    label="上传ChatGPT导出的JSON文件",
                    file_count="single",
                    file_types=[".json"],
                    type="filepath",
                )
                output_name = gr.Textbox(label="输出文件名（不含扩展名，可选）")
                mode = gr.Radio(
                    choices=["citations", "content_references"],
                    value="citations",
                    label="引用处理模式",
                )
                process_btn = gr.<PERSON><PERSON>("处理文件", variant="primary")
                result_info = gr.Textbox(label="处理结果", interactive=False)

            with gr.Column():
                md_output = gr.Textbox(label="Markdown内容", lines=10)
                bib_output = gr.Textbox(label="BibTeX内容", lines=10)

                with gr.Row():
                    md_download = gr.File(label="下载Markdown文件")
                    bib_download = gr.File(label="下载BibTeX文件")

        # 处理逻辑
        process_btn.click(
            fn=process_file,
            inputs=[file_input, output_name, mode],
            outputs=[md_output, bib_output, result_info],
        )

        # 连接处理结果和下载按钮
        md_output.change(
            fn=download_files,
            inputs=[md_output, bib_output, output_name],
            outputs=[md_download, bib_download],
        )

    return tab
