"""
Gradio界面模块，提供Web UI来处理ChatGPT Deep Research导出
"""

import gradio as gr
from typing import Tuple

from .handlers import (
    process_file,
    process_file_with_docx,
    convert_to_docx,
    download_files,
    translate_text,
    translate_file_content,
    online_translate_text,
    online_translate_file_content,
)

from .markdown_handlers import (
    process_markdown_headings,
    analyze_markdown_headings,
    process_lists_to_headings,
)

from .tabs import (
    create_standard_export_tab,
    create_docx_export_tab,
    create_convert_to_docx_tab,
    create_markdown_headings_tab,
    create_translation_tab,
    create_lists_to_headings_tab,
    create_integrated_tab,
    create_citation_merger_tab,
    create_citation_validator_tab,
    create_online_translation_tab,
)


def create_ui() -> gr.Blocks:
    """
    创建Gradio界面

    Returns:
        Gradio Blocks界面
    """
    # 从配置获取UI标题
    from ..config import get_ui_config
    ui_config = get_ui_config()

    with gr.Blocks(title=ui_config.title) as demo:
        gr.Markdown("# Deep Research 导出工具")

        with gr.Tabs() as tabs:
            # 加载整合工具箱（放在最前面）
            create_integrated_tab()

            # 加载各个独立标签页
            create_standard_export_tab()
            create_docx_export_tab()
            create_convert_to_docx_tab()
            create_markdown_headings_tab()
            create_lists_to_headings_tab()
            create_citation_merger_tab()
            create_citation_validator_tab()
            create_translation_tab()
            create_online_translation_tab()

    return demo


def launch_ui(share: bool = None, **kwargs) -> None:
    """
    启动Gradio界面

    参数:
        share: 是否公开分享界面，默认从配置读取
    """
    # 从配置获取默认值
    from ..config import get_ui_config
    ui_config = get_ui_config()

    if share is None:
        share = ui_config.share

    # 合并配置参数
    launch_kwargs = {
        "share": share,
        "server_name": ui_config.server_name,
        "auth": ui_config.auth,
    }

    if ui_config.server_port:
        launch_kwargs["server_port"] = ui_config.server_port
    if ui_config.theme:
        launch_kwargs["theme"] = ui_config.theme

    # 用户提供的参数优先
    launch_kwargs.update(kwargs)

    demo = create_ui()
    demo.launch(**launch_kwargs)


if __name__ == "__main__":
    launch_ui()
