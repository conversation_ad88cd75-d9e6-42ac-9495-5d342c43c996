# DeepResearch Exporter 配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# LLM翻译配置 (用于本地LLM翻译功能)
# =============================================================================

# LLM API基础URL (默认为Ollama本地地址)
LLM_TRANSLATION_BASE_URL=http://localhost:11434/v1

# LLM API密钥 (Ollama本地使用时可以是任意值)
LLM_TRANSLATION_API_KEY=ollama

# LLM模型名称
LLM_TRANSLATION_MODEL=qwen2.5:32b

# 翻译源语言
LLM_TRANSLATION_SOURCE_LANGUAGE=English

# 翻译目标语言
LLM_TRANSLATION_TARGET_LANGUAGE=Chinese

# 专业领域 (可选，如：医学、法律、技术等)
# LLM_TRANSLATION_DOMAIN=

# 温度参数 (0.0-1.0，控制翻译的创造性)
LLM_TRANSLATION_TEMPERATURE=0.3

# Top-P参数 (0.0-1.0，控制词汇选择的多样性)
LLM_TRANSLATION_TOP_P=0.9

# 批处理大小 (同时处理的文本段数量)
LLM_TRANSLATION_BATCH_SIZE=5

# 最大Token数
LLM_TRANSLATION_MAX_TOKENS=4000

# 最大并发请求数
LLM_TRANSLATION_MAX_REQUESTS=5

# =============================================================================
# 在线翻译配置 (Google Translate / DeepL API)
# =============================================================================

# 翻译器类型: google 或 deepl
ONLINE_TRANSLATOR_TYPE=google

# DeepL API密钥 (使用DeepL时必需)
# DEEPL_API_KEY=your_deepl_api_key_here

# 在线翻译源语言 (留空为自动检测)
# ONLINE_TRANSLATION_SOURCE_LANGUAGE=

# 在线翻译目标语言
ONLINE_TRANSLATION_TARGET_LANGUAGE=zh-CN

# DeepL正式程度: default, more, less (仅DeepL支持)
# ONLINE_TRANSLATION_FORMALITY=default

# 是否保留格式 (true/false)
ONLINE_TRANSLATION_PRESERVE_FORMATTING=false

# 分句策略: nonewlines, default, no
ONLINE_TRANSLATION_SPLIT_SENTENCES=nonewlines

# 标签处理方式: xml, html (可选)
# ONLINE_TRANSLATION_TAG_HANDLING=

# DeepL模型类型: quality_optimized, prefer_quality_optimized (可选)
# ONLINE_TRANSLATION_MODEL_TYPE=

# =============================================================================
# 导出配置
# =============================================================================

# 默认输出目录
EXPORT_OUTPUT_DIR=output

# 默认处理模式: citations 或 content_references
EXPORT_DEFAULT_MODE=citations

# 默认CSL样式文件路径 (可选)
# EXPORT_DEFAULT_CSL_PATH=templates/china-national-standard-gb-t-7714-2015-numeric.csl

# 默认Word参考文档路径 (可选)
# EXPORT_DEFAULT_REFERENCE_DOC=templates/custom-reference.docx

# 是否自动创建DOCX文件 (true/false)
EXPORT_AUTO_CREATE_DOCX=false

# =============================================================================
# Web UI配置
# =============================================================================

# UI标题
UI_TITLE=Deep Research 导出工具

# UI主题 (可选，如：default, soft, monochrome等)
# UI_THEME=

# 是否启用公共分享 (true/false)
UI_SHARE=false

# 服务器绑定地址
UI_SERVER_NAME=127.0.0.1

# 服务器端口 (可选，留空为自动分配)
# UI_SERVER_PORT=7860

# UI认证用户名 (可选，启用基本认证)
# UI_AUTH_USERNAME=admin

# UI认证密码 (可选，启用基本认证)
# UI_AUTH_PASSWORD=password

# 最大文件上传大小 (字节)
UI_MAX_FILE_SIZE=104857600

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 是否显示时间戳 (true/false)
LOG_SHOW_TIME=true

# 是否显示文件路径 (true/false)
LOG_SHOW_PATH=false

# 日志文件路径 (可选，留空为仅控制台输出)
# LOG_FILE=logs/app.log

# =============================================================================
# 其他配置说明
# =============================================================================

# 1. 环境变量优先级：
#    环境变量 > .env文件 > 代码中的默认值

# 2. 布尔值配置：
#    使用 true/false (不区分大小写)

# 3. 数值配置：
#    确保使用有效的数字格式

# 4. 路径配置：
#    可以使用相对路径或绝对路径
#    相对路径相对于项目根目录

# 5. API密钥安全：
#    请妥善保管API密钥，不要提交到版本控制系统
#    建议使用环境变量或安全的密钥管理服务
