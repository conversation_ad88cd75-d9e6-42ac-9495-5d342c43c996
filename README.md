# DeepResearch Exporter

[![Python Version](https://img.shields.io/badge/python-3.12+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

一个用于处理ChatGPT Deep Research模式导出对话的工具，可将其转换为学术友好的Markdown和BibTeX格式。

## ✨ 功能特点

- 📄 **智能解析**：解析ChatGPT导出的JSON文件，识别Deep Research模式回复
- 📚 **引用提取**：自动提取和格式化文献引用信息
- 📝 **格式转换**：生成标准Markdown和符合学术标准的BibTeX文件
- 🌐 **Web界面**：提供直观的图形化操作界面
- 🔧 **命令行工具**：支持批量处理和自动化工作流
- 🌍 **翻译功能**：集成LLM和在线翻译服务
- ⚙️ **灵活配置**：支持环境变量和配置文件
- 🧪 **完整测试**：包含全面的单元测试和集成测试
- 📖 **详细文档**：提供完整的使用指南和API文档

## 📦 安装

### 系统要求

- Python 3.12+
- 推荐使用 [uv](https://github.com/astral-sh/uv) 进行依赖管理

### 从源码安装

```bash
# 克隆仓库
git clone https://github.com/zenor0/deepresearch-exporter.git
cd deepresearch-exporter

# 使用 uv 安装（推荐）
uv sync

# 或使用 pip 安装
pip install -e .

# 安装开发依赖（如果需要开发）
pip install -e ".[dev,test,docs]"
```

### 快速开始

```bash
# 安装项目
make install

# 安装开发环境
make install-dev

# 运行测试
make test

# 启动Web界面
make web
```

## 🚀 使用方法

### 配置

首次使用前，建议创建配置文件：

```bash
# 复制示例配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

详细配置说明请参考 [配置指南](docs/configuration.md)。

### Web界面

启动Web界面：

```bash
# 基本启动
deepresearch-web

# 或使用 make 命令
make web

# 启动并绑定到所有接口（用于服务器部署）
make web-server
```

创建可公开访问的链接：

```bash
deepresearch-web --share
```

启动后，在浏览器中打开显示的链接，即可通过图形界面上传和处理文件。

### 命令行

基本用法：

```bash
# 处理单个文件
deepresearch-export path/to/chatgpt_export.json

# 指定输出目录
deepresearch-export input.json -o output/

# 使用 make 命令
make run ARGS="input.json -o output/"
```

指定输出目录：

```bash
deepresearch-export path/to/chatgpt_export.json --output-dir path/to/output
```

指定输出文件名：

```bash
deepresearch-export path/to/chatgpt_export.json --name my_research_paper
```

选择引用处理模式：

```bash
deepresearch-export path/to/chatgpt_export.json --mode content_references
```

### 作为库使用

#### 基础用法（向后兼容）

```python
from deepresearch_exporter.parser import process_conversation
from deepresearch_exporter.exporter import export_response

# 处理对话
response = process_conversation("path/to/chatgpt_export.json")

# 导出为Markdown和BibTeX
md_path, bib_path = export_response(response, "output", "my_research_paper")
```

#### 新的面向对象API

```python
from deepresearch_exporter import ConversationProcessor, DocumentExporter, TranslationService

# 处理对话
processor = ConversationProcessor()
response = processor.process_file("chatgpt_export.json")

# 导出文档
exporter = DocumentExporter("output")
md_path, bib_path = exporter.export_markdown_and_bibtex(response, "research")

# 翻译服务
translation_service = TranslationService()
translated_text = await translation_service.translate_text_llm("Hello, world!")
```

### 文档翻译功能

通过Web界面可以使用翻译功能：

1. 打开Web界面，切换到"文档翻译"标签页
2. 选择"文本翻译"或"文件翻译"
3. 输入文本或上传文件
4. 配置翻译参数（源语言、目标语言、模型等）
5. 点击"翻译"按钮

翻译功能支持的参数：
- 源语言和目标语言
- 专业领域（可选）
- 模型名称（默认为qwen2.5:32b）
- API地址（默认为Ollama本地地址http://localhost:11434/v1）
- API密钥
- 温度和Top-P参数
- 批处理大小、最大Token数和最大并发请求数（仅文件翻译）

也可以作为库使用翻译功能：

```python
from deepresearch_exporter.models import TranslationConfig
from deepresearch_exporter.translator import Translator

# 创建翻译配置
config = TranslationConfig(
    base_url="http://localhost:11434/v1",
    api_key="ollama",
    model="qwen2.5:32b",
    source_language="English",
    target_language="Chinese"
)

# 初始化翻译器
translator = Translator(config)

# 翻译文本
result = translator.translate_text("Hello, world!")
print(result.translation)  # 你好，世界！

# 翻译文件
output_path, error = translator.translate_file("input.txt", "output.txt")
if not error:
    print(f"文件已翻译并保存到: {output_path}")
else:
    print(f"翻译失败: {error}")
```

## 输出示例

处理后将生成两个文件：

1. `my_research_paper.md` - 包含正文内容，引用已转换为Markdown格式
2. `my_research_paper.bib` - 包含BibTeX格式的引用列表

## 后续处理

生成的Markdown和BibTeX文件可以使用Pandoc进一步转换为其他格式：

```bash
pandoc my_research_paper.md --bibliography=my_research_paper.bib -o my_research_paper.pdf
```

## 📁 项目结构

```
deepresearch_exporter/
├── __init__.py              # 主模块入口
├── cli.py                   # 命令行接口
├── config.py                # 配置管理模块
├── models.py                # 数据模型定义
├── logger.py                # 日志配置
├── core/                    # 核心功能模块
│   ├── __init__.py
│   ├── processor.py         # 对话处理器
│   ├── exporter.py          # 文档导出器
│   └── validator.py         # 引用验证器
├── services/                # 服务模块
│   ├── __init__.py
│   └── translation.py       # 翻译服务
├── tools/                   # 工具模块
│   ├── __init__.py
│   ├── markdown.py          # Markdown处理工具
│   └── bibtex.py           # BibTeX处理工具
├── parser/                  # 解析器模块（向后兼容）
├── exporter.py             # 导出功能（向后兼容）
├── translator.py           # LLM翻译器
├── online_translator.py    # 在线翻译器
└── ui/                     # Web界面
    ├── app.py              # Gradio应用
    ├── handlers.py         # 事件处理器
    └── tabs/               # 界面标签页
```

## 🛠️ 开发指南

### 开发环境设置

```bash
# 克隆仓库
git clone https://github.com/zenor0/deepresearch-exporter.git
cd deepresearch-exporter

# 设置开发环境
make install-dev
make pre-commit
```

### 开发工作流

```bash
# 代码格式化
make format

# 代码检查
make lint

# 类型检查
make type-check

# 运行测试
make test

# 运行所有检查
make all-checks
```

### 测试

```bash
# 运行所有测试
make test

# 运行测试并生成覆盖率报告
make test-cov

# 运行特定测试
pytest tests/test_config.py
```

## 📚 文档

- [配置指南](docs/configuration.md) - 详细的配置选项说明

## 🤝 贡献

欢迎贡献代码！请确保：
- 代码通过所有测试
- 遵循代码风格规范
- 添加适当的测试

## 📄 许可证

本项目采用MIT许可证。
