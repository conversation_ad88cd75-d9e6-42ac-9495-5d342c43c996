"""
pytest配置文件，包含测试夹具和配置
"""

import pytest
import tempfile
import json
from pathlib import Path
from typing import Dict, Any

from deepresearch_exporter.models import DeepResearchResponse, Citation


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def sample_citation():
    """示例引用"""
    return Citation(
        key="test2024",
        title="Test Article",
        authors=["<PERSON>", "<PERSON>"],
        year="2024",
        url="https://example.com/test",
        doi="10.1000/test",
        journal="Test Journal",
        entry_type="article"
    )


@pytest.fixture
def sample_response(sample_citation):
    """示例Deep Research回复"""
    return DeepResearchResponse(
        content="# Test Content\n\nThis is a test [@test2024].",
        citations=[sample_citation],
        metadata={"source": "test"}
    )


@pytest.fixture
def sample_chatgpt_export():
    """示例ChatGPT导出JSON数据"""
    return {
        "title": "Test Conversation",
        "create_time": 1234567890,
        "mapping": {
            "msg1": {
                "id": "msg1",
                "message": {
                    "id": "msg1",
                    "author": {"role": "assistant"},
                    "content": {
                        "content_type": "text",
                        "parts": ["# Test Content\n\nThis is a test response with citations [@test2024]."]
                    },
                    "metadata": {
                        "citations": [
                            {
                                "metadata": {
                                    "title": "Test Article",
                                    "url": "https://example.com/test",
                                    "text": "Test citation text"
                                }
                            }
                        ]
                    }
                }
            }
        }
    }


@pytest.fixture
def sample_chatgpt_file(temp_dir, sample_chatgpt_export):
    """创建示例ChatGPT导出文件"""
    file_path = temp_dir / "test_export.json"
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(sample_chatgpt_export, f, ensure_ascii=False, indent=2)
    return file_path


@pytest.fixture
def sample_markdown_content():
    """示例Markdown内容"""
    return """# Test Document

This is a test document with citations.

## Introduction

This section introduces the topic [@test2024].

## Methods

The methods are described here [@another2024].

## Results

Results show interesting findings [@test2024].
"""


@pytest.fixture
def sample_bibtex_content():
    """示例BibTeX内容"""
    return """@article{test2024,
    title={Test Article},
    author={John Doe and Jane Smith},
    journal={Test Journal},
    year={2024},
    url={https://example.com/test},
    doi={10.1000/test}
}

@article{another2024,
    title={Another Test Article},
    author={Alice Johnson},
    journal={Another Journal},
    year={2024},
    url={https://example.com/another}
}
"""


@pytest.fixture
def sample_markdown_file(temp_dir, sample_markdown_content):
    """创建示例Markdown文件"""
    file_path = temp_dir / "test.md"
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(sample_markdown_content)
    return file_path


@pytest.fixture
def sample_bibtex_file(temp_dir, sample_bibtex_content):
    """创建示例BibTeX文件"""
    file_path = temp_dir / "test.bib"
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(sample_bibtex_content)
    return file_path


@pytest.fixture
def mock_config():
    """模拟配置"""
    from deepresearch_exporter.config import AppConfig, LLMTranslationConfig, OnlineTranslationConfig
    
    return AppConfig(
        llm_translation=LLMTranslationConfig(
            base_url="http://localhost:11434/v1",
            api_key="test_key",
            model="test_model"
        ),
        online_translation=OnlineTranslationConfig(
            translator_type="google",
            api_key="test_deepl_key"
        )
    )
