"""
配置模块测试
"""

import os
import pytest
from unittest.mock import patch

from deepresearch_exporter.config import (
    LLMTranslationConfig,
    OnlineTranslationConfig,
    ExportConfig,
    UIConfig,
    LoggingConfig,
    AppConfig,
    get_config,
    reload_config
)


class TestLLMTranslationConfig:
    """LLM翻译配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = LLMTranslationConfig()
        assert config.base_url == "http://localhost:11434/v1"
        assert config.api_key == "ollama"
        assert config.model == "qwen2.5:32b"
        assert config.source_language == "English"
        assert config.target_language == "Chinese"
        assert config.temperature == 0.3
        assert config.top_p == 0.9
        assert config.batch_size == 5
        assert config.max_tokens == 4000
        assert config.max_requests == 5
    
    @patch.dict(os.environ, {
        'LLM_TRANSLATION_BASE_URL': 'http://test.com',
        'LLM_TRANSLATION_API_KEY': 'test_key',
        'LLM_TRANSLATION_MODEL': 'test_model',
        'LLM_TRANSLATION_TEMPERATURE': '0.5',
        'LLM_TRANSLATION_BATCH_SIZE': '10'
    })
    def test_from_env(self):
        """测试从环境变量创建配置"""
        config = LLMTranslationConfig.from_env()
        assert config.base_url == "http://test.com"
        assert config.api_key == "test_key"
        assert config.model == "test_model"
        assert config.temperature == 0.5
        assert config.batch_size == 10


class TestOnlineTranslationConfig:
    """在线翻译配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = OnlineTranslationConfig()
        assert config.translator_type == "google"
        assert config.api_key == ""
        assert config.target_language == "zh-CN"
        assert config.preserve_formatting is False
        assert config.split_sentences == "nonewlines"
    
    @patch.dict(os.environ, {
        'ONLINE_TRANSLATOR_TYPE': 'deepl',
        'DEEPL_API_KEY': 'test_deepl_key',
        'ONLINE_TRANSLATION_TARGET_LANGUAGE': 'zh',
        'ONLINE_TRANSLATION_PRESERVE_FORMATTING': 'true'
    })
    def test_from_env(self):
        """测试从环境变量创建配置"""
        config = OnlineTranslationConfig.from_env()
        assert config.translator_type == "deepl"
        assert config.api_key == "test_deepl_key"
        assert config.target_language == "zh"
        assert config.preserve_formatting is True


class TestExportConfig:
    """导出配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = ExportConfig()
        assert config.output_dir == "output"
        assert config.default_mode == "citations"
        assert config.auto_create_docx is False
    
    @patch.dict(os.environ, {
        'EXPORT_OUTPUT_DIR': 'custom_output',
        'EXPORT_DEFAULT_MODE': 'content_references',
        'EXPORT_AUTO_CREATE_DOCX': 'true'
    })
    def test_from_env(self):
        """测试从环境变量创建配置"""
        config = ExportConfig.from_env()
        assert config.output_dir == "custom_output"
        assert config.default_mode == "content_references"
        assert config.auto_create_docx is True


class TestUIConfig:
    """UI配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = UIConfig()
        assert config.title == "Deep Research 导出工具"
        assert config.share is False
        assert config.server_name == "127.0.0.1"
        assert config.auth is None
        assert config.max_file_size == 100 * 1024 * 1024
    
    @patch.dict(os.environ, {
        'UI_TITLE': 'Custom Title',
        'UI_SHARE': 'true',
        'UI_SERVER_NAME': '0.0.0.0',
        'UI_SERVER_PORT': '8080',
        'UI_AUTH_USERNAME': 'admin',
        'UI_AUTH_PASSWORD': 'password',
        'UI_MAX_FILE_SIZE': '50000000'
    })
    def test_from_env(self):
        """测试从环境变量创建配置"""
        config = UIConfig.from_env()
        assert config.title == "Custom Title"
        assert config.share is True
        assert config.server_name == "0.0.0.0"
        assert config.server_port == 8080
        assert config.auth == ("admin", "password")
        assert config.max_file_size == 50000000


class TestLoggingConfig:
    """日志配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = LoggingConfig()
        assert config.level == "INFO"
        assert config.show_time is True
        assert config.show_path is False
        assert config.log_file is None
    
    @patch.dict(os.environ, {
        'LOG_LEVEL': 'DEBUG',
        'LOG_SHOW_TIME': 'false',
        'LOG_SHOW_PATH': 'true',
        'LOG_FILE': '/var/log/app.log'
    })
    def test_from_env(self):
        """测试从环境变量创建配置"""
        config = LoggingConfig.from_env()
        assert config.level == "DEBUG"
        assert config.show_time is False
        assert config.show_path is True
        assert config.log_file == "/var/log/app.log"


class TestAppConfig:
    """应用配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        config = AppConfig()
        assert isinstance(config.llm_translation, LLMTranslationConfig)
        assert isinstance(config.online_translation, OnlineTranslationConfig)
        assert isinstance(config.export, ExportConfig)
        assert isinstance(config.ui, UIConfig)
        assert isinstance(config.logging, LoggingConfig)
    
    def test_to_dict(self):
        """测试转换为字典"""
        config = AppConfig()
        config_dict = config.to_dict()
        
        assert "llm_translation" in config_dict
        assert "online_translation" in config_dict
        assert "export" in config_dict
        assert "ui" in config_dict
        assert "logging" in config_dict
        
        assert isinstance(config_dict["llm_translation"], dict)
        assert "base_url" in config_dict["llm_translation"]


class TestConfigFunctions:
    """配置函数测试"""
    
    def test_get_config(self):
        """测试获取配置"""
        config = get_config()
        assert isinstance(config, AppConfig)
    
    def test_reload_config(self):
        """测试重新加载配置"""
        config = reload_config()
        assert isinstance(config, AppConfig)
