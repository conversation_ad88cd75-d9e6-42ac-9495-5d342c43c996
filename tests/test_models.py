"""
模型测试
"""

import pytest
from deepresearch_exporter.models import Citation, DeepResearchResponse, TranslationConfig


class TestCitation:
    """引用模型测试"""
    
    def test_creation(self):
        """测试创建引用"""
        citation = Citation(
            key="test2024",
            title="Test Article",
            authors=["<PERSON>"],
            year="2024",
            url="https://example.com",
            doi="10.1000/test"
        )
        
        assert citation.key == "test2024"
        assert citation.title == "Test Article"
        assert citation.authors == ["<PERSON>"]
        assert citation.year == "2024"
        assert citation.url == "https://example.com"
        assert citation.doi == "10.1000/test"
    
    def test_to_bibtex(self):
        """测试转换为BibTeX"""
        citation = Citation(
            key="test2024",
            title="Test Article",
            authors=["<PERSON>", "<PERSON>"],
            year="2024",
            url="https://example.com",
            doi="10.1000/test",
            journal="Test Journal",
            entry_type="article"
        )
        
        bibtex = citation.to_bibtex()
        
        assert "@article{test2024," in bibtex
        assert "title={Test Article}" in bibtex
        assert "author={<PERSON> and <PERSON>}" in bibtex
        assert "year={2024}" in bibtex
        assert "url={https://example.com}" in bibtex
        assert "doi={10.1000/test}" in bibtex
        assert "journal={Test Journal}" in bibtex
    
    def test_to_bibtex_minimal(self):
        """测试最小BibTeX转换"""
        citation = Citation(
            key="minimal2024",
            title="Minimal Article",
            authors=["Author"],
            year="2024"
        )
        
        bibtex = citation.to_bibtex()
        
        assert "@misc{minimal2024," in bibtex
        assert "title={Minimal Article}" in bibtex
        assert "author={Author}" in bibtex
        assert "year={2024}" in bibtex
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "key": "dict2024",
            "title": "Dict Article",
            "authors": ["Dict Author"],
            "year": "2024",
            "url": "https://dict.example.com"
        }
        
        citation = Citation.from_dict(data)
        
        assert citation.key == "dict2024"
        assert citation.title == "Dict Article"
        assert citation.authors == ["Dict Author"]
        assert citation.year == "2024"
        assert citation.url == "https://dict.example.com"
    
    def test_to_dict(self):
        """测试转换为字典"""
        citation = Citation(
            key="todict2024",
            title="To Dict Article",
            authors=["To Dict Author"],
            year="2024"
        )
        
        data = citation.to_dict()
        
        assert data["key"] == "todict2024"
        assert data["title"] == "To Dict Article"
        assert data["authors"] == ["To Dict Author"]
        assert data["year"] == "2024"


class TestDeepResearchResponse:
    """Deep Research回复测试"""
    
    def test_creation(self, sample_citation):
        """测试创建回复"""
        response = DeepResearchResponse(
            content="# Test\n\nContent [@test2024]",
            citations=[sample_citation],
            metadata={"source": "test"}
        )
        
        assert response.content == "# Test\n\nContent [@test2024]"
        assert len(response.citations) == 1
        assert response.citations[0] == sample_citation
        assert response.metadata == {"source": "test"}
    
    def test_get_citation_keys(self, sample_citation):
        """测试获取引用键"""
        response = DeepResearchResponse(
            content="Content",
            citations=[sample_citation]
        )
        
        keys = response.get_citation_keys()
        assert keys == ["test2024"]
    
    def test_get_citations_bibtex(self, sample_citation):
        """测试获取BibTeX"""
        response = DeepResearchResponse(
            content="Content",
            citations=[sample_citation]
        )
        
        bibtex = response.get_citations_bibtex()
        assert "@article{test2024," in bibtex
    
    def test_to_dict(self, sample_citation):
        """测试转换为字典"""
        response = DeepResearchResponse(
            content="Content",
            citations=[sample_citation],
            metadata={"test": "value"}
        )
        
        data = response.to_dict()
        
        assert data["content"] == "Content"
        assert len(data["citations"]) == 1
        assert data["metadata"] == {"test": "value"}
    
    def test_from_dict(self, sample_citation):
        """测试从字典创建"""
        data = {
            "content": "Dict Content",
            "citations": [sample_citation.to_dict()],
            "metadata": {"source": "dict"}
        }
        
        response = DeepResearchResponse.from_dict(data)
        
        assert response.content == "Dict Content"
        assert len(response.citations) == 1
        assert response.citations[0].key == sample_citation.key
        assert response.metadata == {"source": "dict"}


class TestTranslationConfig:
    """翻译配置测试"""
    
    def test_creation(self):
        """测试创建配置"""
        config = TranslationConfig(
            base_url="http://test.com",
            api_key="test_key",
            model="test_model"
        )
        
        assert config.base_url == "http://test.com"
        assert config.api_key == "test_key"
        assert config.model == "test_model"
    
    def test_default_values(self):
        """测试默认值"""
        config = TranslationConfig()
        
        assert config.base_url == "http://localhost:11434/v1"
        assert config.api_key == "ollama"
        assert config.model == "qwen2.5:32b"
        assert config.source_language == "English"
        assert config.target_language == "Chinese"
        assert config.temperature == 0.3
        assert config.top_p == 0.9
        assert config.batch_size == 5
        assert config.max_tokens == 4000
        assert config.max_requests == 5
    
    def test_from_config(self, mock_config):
        """测试从配置创建"""
        with pytest.MonkeyPatch().context() as m:
            m.setattr("deepresearch_exporter.config.get_llm_translation_config", 
                     lambda: mock_config.llm_translation)
            
            config = TranslationConfig.from_config()
            
            assert config.base_url == "http://localhost:11434/v1"
            assert config.api_key == "test_key"
            assert config.model == "test_model"
