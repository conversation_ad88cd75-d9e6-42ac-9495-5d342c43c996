"""
核心功能测试
"""

import pytest
from unittest.mock import Mock, patch
from pathlib import Path

from deepresearch_exporter.core import ConversationProcessor, DocumentExporter, CitationValidator
from deepresearch_exporter.models import DeepResearchResponse, Citation


class TestConversationProcessor:
    """对话处理器测试"""
    
    def test_init(self):
        """测试初始化"""
        processor = ConversationProcessor()
        assert processor is not None
    
    @patch('deepresearch_exporter.core.processor.ConversationProcessor.process_conversation')
    def test_process_file(self, mock_process, sample_response):
        """测试处理文件"""
        mock_process.return_value = sample_response

        processor = ConversationProcessor()
        result = processor.process_file("test.json", "citations")

        assert result == sample_response
        mock_process.assert_called_once_with("test.json", "citations")
    
    @patch('deepresearch_exporter.core.processor.ConversationProcessor.load_conversation')
    def test_validate_file_valid(self, mock_load):
        """测试验证有效文件"""
        mock_load.return_value = {"test": "data"}

        processor = ConversationProcessor()
        result = processor.validate_file("valid.json")

        assert result is True
        mock_load.assert_called_once_with("valid.json")
    
    @patch('deepresearch_exporter.core.processor.ConversationProcessor.load_conversation')
    def test_validate_file_invalid(self, mock_load):
        """测试验证无效文件"""
        mock_load.side_effect = Exception("Invalid file")

        processor = ConversationProcessor()
        result = processor.validate_file("invalid.json")

        assert result is False


class TestDocumentExporter:
    """文档导出器测试"""
    
    def test_init(self, temp_dir):
        """测试初始化"""
        output_dir = temp_dir / "output"
        exporter = DocumentExporter(str(output_dir))
        
        assert exporter.output_dir == str(output_dir)
        assert output_dir.exists()
    
    @patch('deepresearch_exporter.core.exporter._export_response')
    def test_export_markdown_and_bibtex(self, mock_export, sample_response, temp_dir):
        """测试导出Markdown和BibTeX"""
        expected_md_path = str(temp_dir / "test.md")
        expected_bib_path = str(temp_dir / "test.bib")
        mock_export.return_value = (expected_md_path, expected_bib_path)

        exporter = DocumentExporter(str(temp_dir))
        md_path, bib_path = exporter.export_markdown_and_bibtex(sample_response, "test")

        assert md_path == expected_md_path
        assert bib_path == expected_bib_path
        mock_export.assert_called_once_with(sample_response, str(temp_dir), "test")
    
    @patch('deepresearch_exporter.core.exporter._export_response_with_docx')
    def test_export_with_docx(self, mock_export, sample_response, temp_dir):
        """测试导出包含DOCX"""
        expected_md_path = str(temp_dir / "test.md")
        expected_bib_path = str(temp_dir / "test.bib")
        expected_docx_path = str(temp_dir / "test.docx")
        mock_export.return_value = (expected_md_path, expected_bib_path, expected_docx_path)

        exporter = DocumentExporter(str(temp_dir))
        md_path, bib_path, docx_path = exporter.export_with_docx(
            sample_response, "test", "style.csl", "ref.docx"
        )

        assert md_path == expected_md_path
        assert bib_path == expected_bib_path
        assert docx_path == expected_docx_path
        mock_export.assert_called_once_with(
            sample_response, str(temp_dir), "test", "style.csl", "ref.docx"
        )
    
    @patch('deepresearch_exporter.core.exporter._export_docx')
    def test_convert_to_docx(self, mock_export, temp_dir):
        """测试转换为DOCX"""
        mock_export.return_value = "output.docx"

        exporter = DocumentExporter(str(temp_dir))
        result = exporter.convert_to_docx(
            "test.md", "test.bib", "output.docx", "style.csl", "ref.docx"
        )

        assert result == "output.docx"
        mock_export.assert_called_once_with(
            "test.md", "test.bib", "output.docx", "style.csl", "ref.docx"
        )


class TestCitationValidator:
    """引用验证器测试"""
    
    def test_init(self):
        """测试初始化"""
        validator = CitationValidator()
        assert validator is not None
    
    @patch('deepresearch_exporter.core.validator.CitationValidator.load_markdown_file')
    @patch('deepresearch_exporter.core.validator.CitationValidator.load_bibtex_keys')
    def test_validate_citations(self, mock_load_bib, mock_load_md):
        """测试验证引用"""
        mock_load_md.return_value = "Test content [@test2024]"
        mock_load_bib.return_value = {"test2024"}

        validator = CitationValidator()
        is_valid, missing, suggestions = validator.validate_citations("test.md", "test.bib")

        assert is_valid is True
        assert missing == []
        assert suggestions == {}
    
    @patch('deepresearch_exporter.core.validator.CitationValidator.validate_citations')
    @patch('deepresearch_exporter.core.validator.CitationValidator.load_markdown_file')
    def test_highlight_missing_citations(self, mock_load_md, mock_validate):
        """测试高亮缺失引用"""
        mock_validate.return_value = (False, ["missing1"], {"missing1": ["similar1"]})
        mock_load_md.return_value = "Test content [@missing1]"

        validator = CitationValidator()
        is_valid, missing, output_path, suggestions = validator.highlight_missing_citations(
            "test.md", "test.bib", "output.md"
        )

        assert is_valid is False
        assert missing == ["missing1"]
        assert output_path == "output.md"
        assert suggestions == {"missing1": ["similar1"]}
    
    def test_generate_report(self):
        """测试生成报告"""
        validator = CitationValidator()
        report = validator.generate_report(["missing1"], {"missing1": ["similar1"]})

        assert "missing1" in report
        assert "similar1" in report
