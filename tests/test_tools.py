"""
工具模块测试
"""

import pytest
from unittest.mock import patch, Mock

from deepresearch_exporter.tools import MarkdownProcessor, BibtexProcessor


class TestMarkdownProcessor:
    """Markdown处理器测试"""
    
    def test_init(self):
        """测试初始化"""
        processor = MarkdownProcessor()
        assert processor is not None
    
    def test_adjust_heading_levels(self):
        """测试调整标题级别"""
        processor = MarkdownProcessor()
        result = processor.adjust_heading_levels("## Original Content", -1)

        assert "# Original Content" in result
    
    def test_extract_heading_structure(self):
        """测试提取标题结构"""
        processor = MarkdownProcessor()
        result = processor.extract_heading_structure("# Title\n## Subtitle")

        assert len(result) == 2
        assert result[0]["level"] == 1
        assert result[0]["content"] == "Title"
        assert result[1]["level"] == 2
        assert result[1]["content"] == "Subtitle"
    
    def test_apply_custom_heading_levels(self):
        """测试应用自定义标题级别"""
        processor = MarkdownProcessor()
        result = processor.apply_custom_heading_levels("# Title", {"Title": 3})

        assert "### Title" in result
    
    def test_convert_lists_to_headings(self):
        """测试转换列表为标题"""
        processor = MarkdownProcessor()
        result = processor.convert_lists_to_headings("- List item", 3, 2)

        assert "## List item" in result
    
    @patch('deepresearch_exporter.parser.bib_merger.merge_markdown_with_bibtex')
    def test_merge_with_bibtex(self, mock_merge):
        """测试与BibTeX合并"""
        mock_merge.return_value = "output.md"
        
        processor = MarkdownProcessor()
        result = processor.merge_with_bibtex(
            "test.md", "test.bib", "output.md", True, "title"
        )
        
        assert result == "output.md"
        mock_merge.assert_called_once_with(
            "test.md", "test.bib", "output.md", True, "title"
        )


class TestBibtexProcessor:
    """BibTeX处理器测试"""
    
    def test_init(self):
        """测试初始化"""
        processor = BibtexProcessor()
        assert processor is not None
    
    @patch('deepresearch_exporter.tools.bibtex.BibtexProcessor.load_bibtex_entries')
    def test_load_bibtex(self, mock_load):
        """测试加载BibTeX"""
        mock_load.return_value = {"key1": {"title": "Test"}}

        processor = BibtexProcessor()
        result = processor.load_bibtex("test.bib")

        assert result == {"key1": {"title": "Test"}}
        mock_load.assert_called_once_with("test.bib")
    
    @patch('deepresearch_exporter.tools.bibtex.format_bibtex_entry')
    def test_format_entry(self, mock_format):
        """测试格式化条目"""
        mock_format.return_value = "@article{key1,\n  title={Test}\n}"

        processor = BibtexProcessor()
        result = processor.format_entry({"title": "Test"})

        assert result == "@article{key1,\n  title={Test}\n}"
        mock_format.assert_called_once_with({"title": "Test"})
    
    @patch('deepresearch_exporter.tools.bibtex.validate_bibtex_entry')
    def test_validate_entry(self, mock_validate):
        """测试验证条目"""
        mock_validate.return_value = True

        processor = BibtexProcessor()
        result = processor.validate_entry({"title": "Test"})

        assert result is True
        mock_validate.assert_called_once_with({"title": "Test"})
    
    @patch('deepresearch_exporter.tools.bibtex.normalize_bibtex_key')
    def test_normalize_key(self, mock_normalize):
        """测试规范化键"""
        mock_normalize.return_value = "normalized_key"

        processor = BibtexProcessor()
        result = processor.normalize_key("Original Key")

        assert result == "normalized_key"
        mock_normalize.assert_called_once_with("Original Key")
    
    def test_merge_entries(self):
        """测试合并条目"""
        entries1 = {"key1": {"title": "Test1"}}
        entries2 = {"key2": {"title": "Test2"}}
        
        processor = BibtexProcessor()
        result = processor.merge_entries(entries1, entries2)
        
        assert result == {"key1": {"title": "Test1"}, "key2": {"title": "Test2"}}
    
    def test_filter_entries_by_type(self):
        """测试按类型过滤条目"""
        entries = {
            "key1": {"type": "article", "title": "Article"},
            "key2": {"type": "book", "title": "Book"},
            "key3": {"type": "article", "title": "Another Article"}
        }
        
        processor = BibtexProcessor()
        result = processor.filter_entries(entries, entry_type="article")
        
        assert len(result) == 2
        assert "key1" in result
        assert "key3" in result
        assert "key2" not in result
    
    def test_filter_entries_by_url(self):
        """测试按URL过滤条目"""
        entries = {
            "key1": {"title": "With URL", "url": "http://example.com"},
            "key2": {"title": "Without URL"},
            "key3": {"title": "Another with URL", "url": "http://test.com"}
        }
        
        processor = BibtexProcessor()
        result = processor.filter_entries(entries, has_url=True)
        
        assert len(result) == 2
        assert "key1" in result
        assert "key3" in result
        assert "key2" not in result
    
    def test_filter_entries_by_doi(self):
        """测试按DOI过滤条目"""
        entries = {
            "key1": {"title": "With DOI", "doi": "10.1000/test"},
            "key2": {"title": "Without DOI"},
            "key3": {"title": "Another with DOI", "doi": "10.1000/another"}
        }
        
        processor = BibtexProcessor()
        result = processor.filter_entries(entries, has_doi=True)
        
        assert len(result) == 2
        assert "key1" in result
        assert "key3" in result
        assert "key2" not in result
