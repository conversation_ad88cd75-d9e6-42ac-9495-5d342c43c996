# 配置指南

DeepResearch Exporter 提供了灵活的配置系统，支持通过环境变量、配置文件和代码参数进行配置。

## 配置优先级

配置的优先级从高到低为：
1. 代码中直接传递的参数
2. 环境变量
3. `.env` 文件中的配置
4. 代码中的默认值

## 配置文件设置

### 1. 创建配置文件

复制 `.env.example` 文件为 `.env`：

```bash
cp .env.example .env
```

### 2. 编辑配置文件

根据您的需求编辑 `.env` 文件中的配置项。

## 配置分类

### LLM翻译配置

用于配置本地LLM翻译功能：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `LLM_TRANSLATION_BASE_URL` | `http://localhost:11434/v1` | LLM API基础URL |
| `LLM_TRANSLATION_API_KEY` | `ollama` | LLM API密钥 |
| `LLM_TRANSLATION_MODEL` | `qwen2.5:32b` | LLM模型名称 |
| `LLM_TRANSLATION_SOURCE_LANGUAGE` | `English` | 翻译源语言 |
| `LLM_TRANSLATION_TARGET_LANGUAGE` | `Chinese` | 翻译目标语言 |
| `LLM_TRANSLATION_DOMAIN` | - | 专业领域（可选） |
| `LLM_TRANSLATION_TEMPERATURE` | `0.3` | 温度参数 (0.0-1.0) |
| `LLM_TRANSLATION_TOP_P` | `0.9` | Top-P参数 (0.0-1.0) |
| `LLM_TRANSLATION_BATCH_SIZE` | `5` | 批处理大小 |
| `LLM_TRANSLATION_MAX_TOKENS` | `4000` | 最大Token数 |
| `LLM_TRANSLATION_MAX_REQUESTS` | `5` | 最大并发请求数 |

### 在线翻译配置

用于配置Google Translate和DeepL API：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `ONLINE_TRANSLATOR_TYPE` | `google` | 翻译器类型 (google/deepl) |
| `DEEPL_API_KEY` | - | DeepL API密钥 |
| `ONLINE_TRANSLATION_SOURCE_LANGUAGE` | - | 源语言（留空为自动检测） |
| `ONLINE_TRANSLATION_TARGET_LANGUAGE` | `zh-CN` | 目标语言 |
| `ONLINE_TRANSLATION_FORMALITY` | - | 正式程度 (default/more/less) |
| `ONLINE_TRANSLATION_PRESERVE_FORMATTING` | `false` | 是否保留格式 |
| `ONLINE_TRANSLATION_SPLIT_SENTENCES` | `nonewlines` | 分句策略 |
| `ONLINE_TRANSLATION_TAG_HANDLING` | - | 标签处理方式 (xml/html) |
| `ONLINE_TRANSLATION_MODEL_TYPE` | - | DeepL模型类型 |

### 导出配置

用于配置文件导出行为：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `EXPORT_OUTPUT_DIR` | `output` | 默认输出目录 |
| `EXPORT_DEFAULT_MODE` | `citations` | 默认处理模式 |
| `EXPORT_DEFAULT_CSL_PATH` | - | 默认CSL样式文件路径 |
| `EXPORT_DEFAULT_REFERENCE_DOC` | - | 默认Word参考文档路径 |
| `EXPORT_AUTO_CREATE_DOCX` | `false` | 是否自动创建DOCX文件 |

### Web UI配置

用于配置Web界面：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `UI_TITLE` | `Deep Research 导出工具` | UI标题 |
| `UI_THEME` | - | UI主题 |
| `UI_SHARE` | `false` | 是否启用公共分享 |
| `UI_SERVER_NAME` | `127.0.0.1` | 服务器绑定地址 |
| `UI_SERVER_PORT` | - | 服务器端口 |
| `UI_AUTH_USERNAME` | - | 认证用户名 |
| `UI_AUTH_PASSWORD` | - | 认证密码 |
| `UI_MAX_FILE_SIZE` | `104857600` | 最大文件上传大小（字节） |

### 日志配置

用于配置日志输出：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `LOG_SHOW_TIME` | `true` | 是否显示时间戳 |
| `LOG_SHOW_PATH` | `false` | 是否显示文件路径 |
| `LOG_FILE` | - | 日志文件路径 |

## 代码中使用配置

### 获取配置

```python
from deepresearch_exporter.config import get_config

# 获取完整配置
config = get_config()

# 获取特定配置
from deepresearch_exporter.config import (
    get_llm_translation_config,
    get_online_translation_config,
    get_export_config,
    get_ui_config,
    get_logging_config
)

llm_config = get_llm_translation_config()
ui_config = get_ui_config()
```

### 重新加载配置

```python
from deepresearch_exporter.config import reload_config

# 重新加载配置（重新读取环境变量和.env文件）
config = reload_config()
```

### 使用配置创建翻译器

```python
from deepresearch_exporter.models import TranslationConfig
from deepresearch_exporter.online_translator import OnlineTranslationConfig

# 从配置创建LLM翻译配置
llm_config = TranslationConfig.from_config()

# 从配置创建在线翻译配置
online_config = OnlineTranslationConfig.from_config()
```

## 配置示例

### 基本配置示例

```bash
# .env 文件示例

# LLM翻译配置
LLM_TRANSLATION_BASE_URL=http://localhost:11434/v1
LLM_TRANSLATION_MODEL=qwen2.5:32b
LLM_TRANSLATION_SOURCE_LANGUAGE=English
LLM_TRANSLATION_TARGET_LANGUAGE=Chinese

# 在线翻译配置
ONLINE_TRANSLATOR_TYPE=deepl
DEEPL_API_KEY=your_deepl_api_key_here

# UI配置
UI_SHARE=false
UI_SERVER_NAME=0.0.0.0
UI_SERVER_PORT=7860

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### 生产环境配置示例

```bash
# 生产环境配置

# 使用远程LLM服务
LLM_TRANSLATION_BASE_URL=https://api.openai.com/v1
LLM_TRANSLATION_API_KEY=your_openai_api_key
LLM_TRANSLATION_MODEL=gpt-4

# 启用认证
UI_AUTH_USERNAME=admin
UI_AUTH_PASSWORD=secure_password

# 绑定到所有接口
UI_SERVER_NAME=0.0.0.0
UI_SERVER_PORT=8080

# 详细日志
LOG_LEVEL=DEBUG
LOG_FILE=/var/log/deepresearch/app.log
```

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要提交到版本控制系统
2. **文件权限**：确保日志文件目录有写入权限
3. **端口冲突**：确保指定的端口未被其他服务占用
4. **网络访问**：如果使用远程LLM服务，确保网络连接正常
5. **配置验证**：启动应用时会验证配置的有效性
