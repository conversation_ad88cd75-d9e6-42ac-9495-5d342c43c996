# DeepResearch Exporter - 产品需求文档

## 1. 产品概述

DeepResearch Exporter 是一个工具，用于处理从 OpenAI ChatGPT 官方网站导出的对话 JSON 数据，特别针对 Deep Research 模式的对话内容进行解析和格式化，最终生成可用于学术或专业用途的格式化文档。

## 2. 功能需求

### 2.1 数据输入
- 接收从 ChatGPT 官网导出的原始 JSON 格式对话数据
- 支持识别并筛选 Deep Research 模式的对话回复

### 2.2 数据解析
- 使用 Pydantic 进行结构化数据解析
- 从对话中提取以下关键信息：
  - 正文内容
  - 引用列表
  - 引用来源详情

### 2.3 数据转换
- 将提取的内容转换为以下格式：
  - Markdown 格式的正文文档
  - BibTeX 格式的引用文件

### 2.4 引用处理
- 整理所有引用，生成符合 BibTeX 规范的引用条目
- 将正文中的引用标记替换为 Markdown 格式的引用链接
- 确保引用键（cite keys）在正文和引用文件中保持一致

## 3. 技术实现

### 3.1 技术栈
- Python 作为主要开发语言
- Pydantic 用于数据验证和解析
- JSON 处理库

### 3.2 处理流程
1. 读取并解析 ChatGPT 导出的 JSON 文件
2. 识别并提取 Deep Research 模式的对话内容
3. 使用 Pydantic 模型进行数据结构化
4. 提取并格式化引用信息
5. 生成 Markdown 格式的正文文档
6. 生成 BibTeX 格式的引用文件

## 4. 输出成果

### 4.1 Markdown 文档
- 包含格式化的正文内容
- 包含标准化的学术引用标记

### 4.2 BibTeX 文件
- 包含所有引用的详细信息
- 符合学术出版标准的引用格式

## 5. 使用场景

该工具主要用于学术研究和专业写作，使用户能够将 ChatGPT 的 Deep Research 模式生成的内容快速转换为可用于学术论文或专业报告的格式，为后续使用 Pandoc 等工具转换为其他格式（如 PDF、Word、LaTeX 等）做好准备。